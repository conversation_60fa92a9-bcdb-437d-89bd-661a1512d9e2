/**
 * 饼图组件
 */
@Component
export struct PieChart {
  @Prop data: ChartData[];
  @Prop size: number = 200;
  @Prop showLabels: boolean = true;
  @Prop showLegend: boolean = true;

  build() {
    Column() {
      // 图表区域
      Stack() {
        // 饼图绘制
        this.buildPieChart()
        
        // 中心文字
        if (this.data.length > 0) {
          this.buildCenterText()
        }
      }
      .width(this.size)
      .height(this.size)

      // 图例
      if (this.showLegend) {
        this.buildLegend()
      }
    }
    .alignItems(HorizontalAlign.Center)
  }

  @Builder
  buildPieChart() {
    Canvas(this.getCanvasRenderingContext())
      .width(this.size)
      .height(this.size)
      .onReady(() => {
        this.drawPieChart();
      })
  }

  @Builder
  buildCenterText() {
    Column() {
      Text('总计')
        .fontSize(12)
        .fontColor('#757575')
        .margin({ bottom: 4 })

      Text(this.getTotalAmount())
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .fontColor('#212121')
    }
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  @Builder
  buildLegend() {
    Column() {
      ForEach(this.data, (item: ChartData, index: number) => {
        Row() {
          // 颜色指示器
          Row()
            .width(12)
            .height(12)
            .backgroundColor(item.color)
            .borderRadius(6)
            .margin({ right: 8 })

          // 标签
          Text(item.label)
            .fontSize(14)
            .fontColor('#212121')
            .layoutWeight(1)

          // 数值
          Text(item.value.toFixed(2))
            .fontSize(14)
            .fontColor('#757575')
            .margin({ right: 8 })

          // 百分比
          Text(`${item.percentage.toFixed(1)}%`)
            .fontSize(12)
            .fontColor('#9E9E9E')
        }
        .width('100%')
        .padding({ top: 8, bottom: 8 })
        .margin({ bottom: index < this.data.length - 1 ? 4 : 0 })
      })
    }
    .width('100%')
    .margin({ top: 16 })
  }

  // 私有方法
  private getCanvasRenderingContext(): CanvasRenderingContext2D {
    // 这里需要返回Canvas的渲染上下文
    // 在实际实现中，需要通过Canvas组件的onReady回调获取
    return {} as CanvasRenderingContext2D;
  }

  private drawPieChart(): void {
    // 简化的饼图绘制逻辑
    // 在实际实现中，这里会使用Canvas API绘制饼图
    console.log('绘制饼图:', this.data);
    
    // TODO: 实现真实的Canvas绘制逻辑
    // 1. 计算每个扇形的角度
    // 2. 使用arc()方法绘制扇形
    // 3. 填充颜色
    // 4. 绘制标签（如果需要）
  }

  private getTotalAmount(): string {
    const total = this.data.reduce((sum, item) => sum + item.value, 0);
    return `¥${total.toFixed(2)}`;
  }
}

/**
 * 简化版饼图组件（使用CSS样式模拟）
 */
@Component
export struct SimplePieChart {
  @Prop data: ChartData[];
  @Prop size: number = 200;

  build() {
    Column() {
      // 使用堆叠的圆形和遮罩来模拟饼图
      Stack() {
        // 背景圆
        Row()
          .width(this.size)
          .height(this.size)
          .borderRadius(this.size / 2)
          .backgroundColor('#E0E0E0')

        // 数据扇形（简化实现）
        ForEach(this.data, (item: ChartData, index: number) => {
          this.buildSegment(item, index)
        })

        // 中心圆
        Row()
          .width(this.size * 0.6)
          .height(this.size * 0.6)
          .borderRadius(this.size * 0.3)
          .backgroundColor('#FFFFFF')

        // 中心文字
        Column() {
          Text('总支出')
            .fontSize(12)
            .fontColor('#757575')

          Text(this.getTotalAmount())
            .fontSize(16)
            .fontWeight(FontWeight.Bold)
            .fontColor('#212121')
            .margin({ top: 4 })
        }
        .justifyContent(FlexAlign.Center)
      }
      .width(this.size)
      .height(this.size)

      // 图例
      this.buildSimpleLegend()
    }
    .alignItems(HorizontalAlign.Center)
  }

  @Builder
  buildSegment(item: ChartData, index: number) {
    // 简化的扇形实现 - 使用边框来模拟
    Row()
      .width(this.size)
      .height(this.size)
      .borderRadius(this.size / 2)
      .border({
        width: 20,
        color: item.color,
        style: BorderStyle.Solid
      })
      .rotate({
        angle: index * 60, // 简化的角度计算
        centerX: '50%',
        centerY: '50%'
      })
  }

  @Builder
  buildSimpleLegend() {
    Column() {
      ForEach(this.data, (item: ChartData, index: number) => {
        Row() {
          Row()
            .width(12)
            .height(12)
            .backgroundColor(item.color)
            .borderRadius(2)
            .margin({ right: 8 })

          Text(item.label)
            .fontSize(14)
            .fontColor('#212121')
            .layoutWeight(1)

          Text(`¥${item.value.toFixed(2)}`)
            .fontSize(14)
            .fontColor('#757575')
        }
        .width('100%')
        .padding({ top: 6, bottom: 6 })
      })
    }
    .width('100%')
    .margin({ top: 16 })
    .padding({ left: 16, right: 16 })
  }

  private getTotalAmount(): string {
    const total = this.data.reduce((sum, item) => sum + item.value, 0);
    return total.toFixed(2);
  }
}

/**
 * 进度环形图组件
 */
@Component
export struct ProgressRing {
  @Prop progress: number = 0; // 0-100
  @Prop size: number = 120;
  @Prop strokeWidth: number = 8;
  @Prop color: string = '#1976D2';
  @Prop backgroundColor: string = '#E0E0E0';
  @Prop showText: boolean = true;

  build() {
    Stack() {
      // 背景环
      Row()
        .width(this.size)
        .height(this.size)
        .borderRadius(this.size / 2)
        .border({
          width: this.strokeWidth,
          color: this.backgroundColor,
          style: BorderStyle.Solid
        })

      // 进度环（简化实现）
      Row()
        .width(this.size)
        .height(this.size)
        .borderRadius(this.size / 2)
        .border({
          width: this.strokeWidth,
          color: this.color,
          style: BorderStyle.Solid
        })
        .clip(true)
        .rotate({
          angle: -90 + (this.progress * 3.6), // 将百分比转换为角度
          centerX: '50%',
          centerY: '50%'
        })

      // 中心文字
      if (this.showText) {
        Text(`${this.progress.toFixed(0)}%`)
          .fontSize(16)
          .fontWeight(FontWeight.Bold)
          .fontColor('#212121')
      }
    }
    .width(this.size)
    .height(this.size)
    .justifyContent(FlexAlign.Center)
    .alignItems(VerticalAlign.Center)
  }
}

// 图表数据接口
export interface ChartData {
  label: string;
  value: number;
  color: string;
  percentage: number;
}

// 图表工具类
export class ChartUtils {
  /**
   * 计算百分比
   */
  static calculatePercentages(data: Omit<ChartData, 'percentage'>[]): ChartData[] {
    const total = data.reduce((sum, item) => sum + item.value, 0);
    
    return data.map(item => ({
      ...item,
      percentage: total > 0 ? (item.value / total) * 100 : 0
    }));
  }

  /**
   * 生成默认颜色
   */
  static getDefaultColors(): string[] {
    return [
      '#FF9800', // 橙色
      '#2196F3', // 蓝色
      '#E91E63', // 粉色
      '#4CAF50', // 绿色
      '#9C27B0', // 紫色
      '#FF5722', // 深橙色
      '#607D8B', // 蓝灰色
      '#795548'  // 棕色
    ];
  }

  /**
   * 为数据分配颜色
   */
  static assignColors(data: Omit<ChartData, 'color' | 'percentage'>[]): Omit<ChartData, 'percentage'>[] {
    const colors = ChartUtils.getDefaultColors();
    
    return data.map((item, index) => ({
      ...item,
      color: colors[index % colors.length]
    }));
  }
}
