import { AccountSummary } from '../models/Account';
import { FormatUtils } from '../common/utils/FormatUtils';

/**
 * 统计卡片组件
 */
@Component
export struct SummaryCard {
  @Prop summary: AccountSummary = new AccountSummary([]);
  @Prop period: string = '本月';

  build() {
    Column() {
      // 标题
      Row() {
        Text(`${this.period}统计`)
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .fontColor('#212121')
        
        Blank()
        
        Text(`共${this.summary.accountCount}笔`)
          .fontSize(14)
          .fontColor('#757575')
      }
      .width('100%')
      .margin({ bottom: 16 })

      // 余额
      Column() {
        Text('余额')
          .fontSize(14)
          .fontColor('#757575')
          .margin({ bottom: 4 })
        
        Text(FormatUtils.formatAmount(this.summary.balance))
          .fontSize(28)
          .fontWeight(FontWeight.Bold)
          .fontColor(this.summary.balance >= 0 ? '#4CAF50' : '#F44336')
      }
      .alignItems(HorizontalAlign.Center)
      .margin({ bottom: 20 })

      // 收支统计
      Row() {
        // 收入
        Column() {
          Row() {
            Text('📈')
              .fontSize(16)
              .margin({ right: 4 })
            
            Text('收入')
              .fontSize(14)
              .fontColor('#757575')
          }
          .margin({ bottom: 4 })
          
          Text(FormatUtils.formatAmount(this.summary.totalIncome))
            .fontSize(18)
            .fontWeight(FontWeight.Bold)
            .fontColor('#4CAF50')
        }
        .layoutWeight(1)
        .alignItems(HorizontalAlign.Center)

        // 分隔线
        Line()
          .width(1)
          .height(40)
          .backgroundColor('#E0E0E0')

        // 支出
        Column() {
          Row() {
            Text('📉')
              .fontSize(16)
              .margin({ right: 4 })
            
            Text('支出')
              .fontSize(14)
              .fontColor('#757575')
          }
          .margin({ bottom: 4 })
          
          Text(FormatUtils.formatAmount(this.summary.totalExpense))
            .fontSize(18)
            .fontWeight(FontWeight.Bold)
            .fontColor('#F44336')
        }
        .layoutWeight(1)
        .alignItems(HorizontalAlign.Center)
      }
      .width('100%')
    }
    .width('100%')
    .padding(20)
    .backgroundColor('#FFFFFF')
    .borderRadius(12)
    .shadow({
      radius: 8,
      color: '#1A000000',
      offsetX: 0,
      offsetY: 4
    })
  }
}
