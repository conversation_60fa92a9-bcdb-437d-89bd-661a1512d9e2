import { Account, AccountType } from '../models/Account';
import { DatabaseService } from '../services/DatabaseService';
import { FormatUtils } from '../common/utils/FormatUtils';
import { DateUtils } from '../common/utils/DateUtils';
import router from '@ohos.router';

@Entry
@Component
struct AccountDetailPage {
  @State account: Account | null = null;
  @State isLoading: boolean = true;
  @State isEditing: boolean = false;
  @State showDeleteDialog: boolean = false;

  private accountId: string = '';
  private databaseService: DatabaseService = DatabaseService.getInstance();

  aboutToAppear() {
    const params = router.getParams() as Record<string, Object>;
    if (params && params['accountId']) {
      this.accountId = params['accountId'] as string;
      this.loadAccount();
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      this.buildHeader()

      if (this.isLoading) {
        this.buildLoadingView()
      } else if (this.account) {
        this.buildAccountDetail()
      } else {
        this.buildErrorView()
      }

      // 删除确认对话框
      if (this.showDeleteDialog) {
        this.buildDeleteDialog()
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder
  buildHeader() {
    Row() {
      // 返回按钮
      Row() {
        Text('←')
          .fontSize(20)
          .fontColor('#212121')
      }
      .width(40)
      .height(40)
      .borderRadius(20)
      .justifyContent(FlexAlign.Center)
      .alignItems(VerticalAlign.Center)
      .onClick(() => {
        router.back();
      })

      Text('账单详情')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .fontColor('#212121')
        .layoutWeight(1)
        .textAlign(TextAlign.Center)

      // 更多操作按钮
      Row() {
        Text('⋯')
          .fontSize(20)
          .fontColor('#212121')
      }
      .width(40)
      .height(40)
      .borderRadius(20)
      .justifyContent(FlexAlign.Center)
      .alignItems(VerticalAlign.Center)
      .onClick(() => {
        this.showMoreActions();
      })
    }
    .width('100%')
    .padding({ left: 16, right: 16, top: 12, bottom: 12 })
    .backgroundColor('#FFFFFF')
  }

  @Builder
  buildLoadingView() {
    Column() {
      Text('⏳')
        .fontSize(48)
        .margin({ bottom: 16 })

      Text('加载中...')
        .fontSize(16)
        .fontColor('#757575')
    }
    .width('100%')
    .layoutWeight(1)
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  @Builder
  buildErrorView() {
    Column() {
      Text('❌')
        .fontSize(48)
        .margin({ bottom: 16 })

      Text('账单不存在')
        .fontSize(16)
        .fontColor('#757575')
        .margin({ bottom: 8 })

      Button('返回')
        .width(100)
        .height(40)
        .fontSize(14)
        .backgroundColor('#1976D2')
        .borderRadius(20)
        .onClick(() => {
          router.back();
        })
    }
    .width('100%')
    .layoutWeight(1)
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  @Builder
  buildAccountDetail() {
    Scroll() {
      Column() {
        // 金额卡片
        this.buildAmountCard()

        // 基本信息
        this.buildBasicInfo()

        // 扩展信息
        this.buildExtendedInfo()

        // 操作按钮
        this.buildActionButtons()
      }
      .padding(16)
    }
    .layoutWeight(1)
  }

  @Builder
  buildAmountCard() {
    Column() {
      // 金额
      Text(this.account!.getFormattedAmount())
        .fontSize(36)
        .fontWeight(FontWeight.Bold)
        .fontColor(this.account!.getAmountColor())
        .margin({ bottom: 8 })

      // 类型标签
      Text(this.account!.type === AccountType.INCOME ? '收入' : '支出')
        .fontSize(14)
        .fontColor('#FFFFFF')
        .backgroundColor(this.account!.getAmountColor())
        .padding({ left: 12, right: 12, top: 4, bottom: 4 })
        .borderRadius(12)
    }
    .width('100%')
    .padding(24)
    .backgroundColor('#FFFFFF')
    .borderRadius(12)
    .alignItems(HorizontalAlign.Center)
    .margin({ bottom: 16 })
  }

  @Builder
  buildBasicInfo() {
    Column() {
      Text('基本信息')
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .fontColor('#212121')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 16 })

      // 分类
      this.buildInfoRow('分类', this.account!.category, '🏷️')

      // 描述
      if (this.account!.description) {
        this.buildInfoRow('描述', this.account!.description, '📝')
      }

      // 日期
      this.buildInfoRow('日期', DateUtils.formatDate(this.account!.date, 'YYYY-MM-DD HH:mm'), '📅')

      // 位置
      if (this.account!.location) {
        this.buildInfoRow('位置', this.account!.location, '📍')
      }
    }
    .width('100%')
    .padding(20)
    .backgroundColor('#FFFFFF')
    .borderRadius(12)
    .margin({ bottom: 16 })
  }

  @Builder
  buildExtendedInfo() {
    Column() {
      Text('扩展信息')
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .fontColor('#212121')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 16 })

      // AI生成标识
      if (this.account!.isAIGenerated) {
        Row() {
          Text('🤖')
            .fontSize(16)
            .margin({ right: 8 })

          Text('AI识别生成')
            .fontSize(14)
            .fontColor('#212121')
            .layoutWeight(1)

          Text(`置信度: ${(this.account!.aiConfidence! * 100).toFixed(0)}%`)
            .fontSize(12)
            .fontColor('#757575')
        }
        .width('100%')
        .margin({ bottom: 12 })
      }

      // 标签
      if (this.account!.tags && this.account!.tags.length > 0) {
        Column() {
          Text('标签')
            .fontSize(14)
            .fontColor('#757575')
            .alignSelf(ItemAlign.Start)
            .margin({ bottom: 8 })

          Flex({ wrap: FlexWrap.Wrap }) {
            ForEach(this.account!.tags, (tag: string) => {
              Text(tag)
                .fontSize(12)
                .fontColor('#1976D2')
                .backgroundColor('#E3F2FD')
                .padding({ left: 8, right: 8, top: 4, bottom: 4 })
                .borderRadius(12)
                .margin({ right: 8, bottom: 8 })
            })
          }
        }
        .width('100%')
        .alignItems(HorizontalAlign.Start)
        .margin({ bottom: 12 })
      }

      // 创建时间
      Text(`创建时间: ${DateUtils.getRelativeTime(this.account!.date)}`)
        .fontSize(12)
        .fontColor('#9E9E9E')
        .alignSelf(ItemAlign.Start)
    }
    .width('100%')
    .padding(20)
    .backgroundColor('#FFFFFF')
    .borderRadius(12)
    .margin({ bottom: 16 })
  }

  @Builder
  buildInfoRow(label: string, value: string, icon: string) {
    Row() {
      Text(icon)
        .fontSize(16)
        .margin({ right: 12 })

      Column() {
        Text(label)
          .fontSize(12)
          .fontColor('#757575')
          .alignSelf(ItemAlign.Start)

        Text(value)
          .fontSize(14)
          .fontColor('#212121')
          .alignSelf(ItemAlign.Start)
          .margin({ top: 2 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Start)
    }
    .width('100%')
    .margin({ bottom: 12 })
  }

  @Builder
  buildActionButtons() {
    Row() {
      Button('编辑')
        .width('45%')
        .height(48)
        .fontSize(16)
        .backgroundColor('#1976D2')
        .borderRadius(8)
        .onClick(() => {
          this.editAccount();
        })

      Button('删除')
        .width('45%')
        .height(48)
        .fontSize(16)
        .backgroundColor('#F44336')
        .borderRadius(8)
        .onClick(() => {
          this.showDeleteDialog = true;
        })
    }
    .width('100%')
    .justifyContent(FlexAlign.SpaceBetween)
    .margin({ top: 20 })
  }

  @Builder
  buildDeleteDialog() {
    Column() {
      Column() {
        Text('确认删除')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .fontColor('#212121')
          .margin({ bottom: 16 })

        Text('确定要删除这条账单记录吗？删除后无法恢复。')
          .fontSize(14)
          .fontColor('#757575')
          .textAlign(TextAlign.Center)
          .margin({ bottom: 24 })

        Row() {
          Button('取消')
            .width('45%')
            .height(40)
            .fontSize(14)
            .backgroundColor('#E0E0E0')
            .fontColor('#212121')
            .borderRadius(8)
            .onClick(() => {
              this.showDeleteDialog = false;
            })

          Button('删除')
            .width('45%')
            .height(40)
            .fontSize(14)
            .backgroundColor('#F44336')
            .borderRadius(8)
            .onClick(() => {
              this.deleteAccount();
            })
        }
        .width('100%')
        .justifyContent(FlexAlign.SpaceBetween)
      }
      .width('80%')
      .padding(24)
      .backgroundColor('#FFFFFF')
      .borderRadius(12)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#********')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .onClick(() => {
      this.showDeleteDialog = false;
    })
  }

  // 私有方法
  private async loadAccount(): Promise<void> {
    this.isLoading = true;
    try {
      this.account = await this.databaseService.getAccountById(this.accountId);
    } catch (error) {
      console.error('加载账单详情失败:', error);
    } finally {
      this.isLoading = false;
    }
  }

  private editAccount(): void {
    if (this.account) {
      router.pushUrl({
        url: 'pages/AddAccountPage',
        params: {
          account: this.account,
          isEdit: true
        }
      });
    }
  }

  private async deleteAccount(): Promise<void> {
    if (this.account) {
      try {
        const success = await this.databaseService.deleteAccount(this.account.id);
        if (success) {
          console.log('账单删除成功');
          router.back();
        } else {
          console.error('账单删除失败');
        }
      } catch (error) {
        console.error('删除账单时出错:', error);
      }
    }
    this.showDeleteDialog = false;
  }

  private showMoreActions(): void {
    console.log('显示更多操作');
    // TODO: 显示更多操作菜单（分享、导出等）
  }
}
