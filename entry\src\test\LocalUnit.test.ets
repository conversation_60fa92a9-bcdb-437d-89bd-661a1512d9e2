import { describe, beforeAll, beforeEach, afterEach, afterAll, it, expect } from '@ohos/hypium';
import { Account, AccountType, AccountSummary } from '../main/ets/models/Account';
import { FormatUtils } from '../main/ets/common/utils/FormatUtils';

export default function localUnitTest() {
  describe('localUnitTest', () => {
    // Defines a test suite. Two parameters are supported: test suite name and test suite function.
    beforeAll(() => {
      // Presets an action, which is performed only once before all test cases of the test suite start.
      // This API supports only one parameter: preset action function.
    });
    beforeEach(() => {
      // Presets an action, which is performed before each unit test case starts.
      // The number of execution times is the same as the number of test cases defined by **it**.
      // This API supports only one parameter: preset action function.
    });
    afterEach(() => {
      // Presets a clear action, which is performed after each unit test case ends.
      // The number of execution times is the same as the number of test cases defined by **it**.
      // This API supports only one parameter: clear action function.
    });
    afterAll(() => {
      // Presets a clear action, which is performed after all test cases of the test suite end.
      // This API supports only one parameter: clear action function.
    });
    it('assertContain', 0, () => {
      // Defines a test case. This API supports three parameters: test case name, filter parameter, and test case function.
      let a = 'abc';
      let b = 'b';
      // Defines a variety of assertion methods, which are used to declare expected boolean conditions.
      expect(a).assertContain(b);
      expect(a).assertEqual(a);
    });

    it('Account Model Test', 0, () => {
      // 测试账单模型
      const account = new Account({
        amount: 25.50,
        type: AccountType.EXPENSE,
        category: '餐饮',
        description: '午餐'
      });

      expect(account.amount).assertEqual(25.50);
      expect(account.type).assertEqual(AccountType.EXPENSE);
      expect(account.category).assertEqual('餐饮');
      expect(account.getFormattedAmount()).assertEqual('-¥25.50');
    });

    it('Account Summary Test', 0, () => {
      // 测试账单汇总
      const accounts = [
        new Account({ amount: 100, type: AccountType.INCOME }),
        new Account({ amount: 50, type: AccountType.EXPENSE }),
        new Account({ amount: 30, type: AccountType.EXPENSE })
      ];

      const summary = new AccountSummary(accounts);

      expect(summary.totalIncome).assertEqual(100);
      expect(summary.totalExpense).assertEqual(80);
      expect(summary.balance).assertEqual(20);
      expect(summary.accountCount).assertEqual(3);
    });

    it('Format Utils Test', 0, () => {
      // 测试格式化工具
      expect(FormatUtils.formatAmount(25.50)).assertEqual('¥25.50');
      expect(FormatUtils.formatAmount(25.50, false)).assertEqual('25.50');
      expect(FormatUtils.formatPercentage(25, 100)).assertEqual('25.0%');
      expect(FormatUtils.isValidEmail('<EMAIL>')).assertTrue();
      expect(FormatUtils.isValidEmail('invalid-email')).assertFalse();
    });
  });
}