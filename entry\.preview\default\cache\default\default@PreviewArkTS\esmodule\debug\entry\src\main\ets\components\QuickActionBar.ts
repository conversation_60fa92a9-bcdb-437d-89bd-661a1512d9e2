if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface QuickActionBar_Params {
    onAddExpense?: () => void;
    onAddIncome?: () => void;
    onPhotoScan?: () => void;
    onVoiceInput?: () => void;
}
export class QuickActionBar extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.onAddExpense = undefined;
        this.onAddIncome = undefined;
        this.onPhotoScan = undefined;
        this.onVoiceInput = undefined;
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: QuickActionBar_Params) {
        if (params.onAddExpense !== undefined) {
            this.onAddExpense = params.onAddExpense;
        }
        if (params.onAddIncome !== undefined) {
            this.onAddIncome = params.onAddIncome;
        }
        if (params.onPhotoScan !== undefined) {
            this.onPhotoScan = params.onPhotoScan;
        }
        if (params.onVoiceInput !== undefined) {
            this.onVoiceInput = params.onVoiceInput;
        }
    }
    updateStateVars(params: QuickActionBar_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
    }
    aboutToBeDeleted() {
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private onAddExpense?: () => void;
    private onAddIncome?: () => void;
    private onPhotoScan?: () => void;
    private onVoiceInput?: () => void;
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/components/QuickActionBar.ets(12:5)", "entry");
            Row.width('100%');
            Row.padding({ left: 20, right: 20, top: 16, bottom: 16 });
            Row.backgroundColor('#FFFFFF');
            Row.borderRadius(12);
            Row.shadow({
                radius: 4,
                color: '#1F000000',
                offsetX: 0,
                offsetY: 2
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 添加支出
            Column.create();
            Column.debugLine("entry/src/main/ets/components/QuickActionBar.ets(14:7)", "entry");
            // 添加支出
            Column.layoutWeight(1);
            // 添加支出
            Column.alignItems(HorizontalAlign.Center);
            // 添加支出
            Column.onClick(() => {
                if (this.onAddExpense) {
                    this.onAddExpense();
                }
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/components/QuickActionBar.ets(15:9)", "entry");
            Row.width(48);
            Row.height(48);
            Row.borderRadius(24);
            Row.backgroundColor('#F44336');
            Row.justifyContent(FlexAlign.Center);
            Row.alignItems(VerticalAlign.Center);
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💸');
            Text.debugLine("entry/src/main/ets/components/QuickActionBar.ets(16:11)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支出');
            Text.debugLine("entry/src/main/ets/components/QuickActionBar.ets(27:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#212121');
        }, Text);
        Text.pop();
        // 添加支出
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 添加收入
            Column.create();
            Column.debugLine("entry/src/main/ets/components/QuickActionBar.ets(40:7)", "entry");
            // 添加收入
            Column.layoutWeight(1);
            // 添加收入
            Column.alignItems(HorizontalAlign.Center);
            // 添加收入
            Column.onClick(() => {
                if (this.onAddIncome) {
                    this.onAddIncome();
                }
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/components/QuickActionBar.ets(41:9)", "entry");
            Row.width(48);
            Row.height(48);
            Row.borderRadius(24);
            Row.backgroundColor('#4CAF50');
            Row.justifyContent(FlexAlign.Center);
            Row.alignItems(VerticalAlign.Center);
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💰');
            Text.debugLine("entry/src/main/ets/components/QuickActionBar.ets(42:11)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('收入');
            Text.debugLine("entry/src/main/ets/components/QuickActionBar.ets(53:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#212121');
        }, Text);
        Text.pop();
        // 添加收入
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 拍照识别
            Column.create();
            Column.debugLine("entry/src/main/ets/components/QuickActionBar.ets(66:7)", "entry");
            // 拍照识别
            Column.layoutWeight(1);
            // 拍照识别
            Column.alignItems(HorizontalAlign.Center);
            // 拍照识别
            Column.onClick(() => {
                if (this.onPhotoScan) {
                    this.onPhotoScan();
                }
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/components/QuickActionBar.ets(67:9)", "entry");
            Row.width(48);
            Row.height(48);
            Row.borderRadius(24);
            Row.backgroundColor('#2196F3');
            Row.justifyContent(FlexAlign.Center);
            Row.alignItems(VerticalAlign.Center);
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📷');
            Text.debugLine("entry/src/main/ets/components/QuickActionBar.ets(68:11)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('拍照');
            Text.debugLine("entry/src/main/ets/components/QuickActionBar.ets(79:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#212121');
        }, Text);
        Text.pop();
        // 拍照识别
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 语音记账
            Column.create();
            Column.debugLine("entry/src/main/ets/components/QuickActionBar.ets(92:7)", "entry");
            // 语音记账
            Column.layoutWeight(1);
            // 语音记账
            Column.alignItems(HorizontalAlign.Center);
            // 语音记账
            Column.onClick(() => {
                if (this.onVoiceInput) {
                    this.onVoiceInput();
                }
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/components/QuickActionBar.ets(93:9)", "entry");
            Row.width(48);
            Row.height(48);
            Row.borderRadius(24);
            Row.backgroundColor('#FF9800');
            Row.justifyContent(FlexAlign.Center);
            Row.alignItems(VerticalAlign.Center);
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🎤');
            Text.debugLine("entry/src/main/ets/components/QuickActionBar.ets(94:11)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('语音');
            Text.debugLine("entry/src/main/ets/components/QuickActionBar.ets(105:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#212121');
        }, Text);
        Text.pop();
        // 语音记账
        Column.pop();
        Row.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
}
