import { AccountType } from "@normalized:N&&&entry/src/main/ets/models/Account&";
import type { Account } from "@normalized:N&&&entry/src/main/ets/models/Account&";
/**
 * AI服务类
 */
export class AIService {
    private static instance: AIService;
    private constructor() { }
    static getInstance(): AIService {
        if (!AIService.instance) {
            AIService.instance = new AIService();
        }
        return AIService.instance;
    }
    /**
     * OCR识别小票信息
     */
    async recognizeReceipt(imagePath: string): Promise<ReceiptInfo> {
        try {
            console.log('开始OCR识别:', imagePath);
            // 模拟OCR识别过程
            await this.delay(2000);
            // 模拟识别结果
            const mockResult: ReceiptInfo = {
                totalAmount: 25.50,
                merchantName: '星巴克咖啡',
                items: [
                    { name: '美式咖啡', price: 20.00, quantity: 1 },
                    { name: '蛋糕', price: 5.50, quantity: 1 }
                ],
                date: new Date(),
                confidence: 0.85,
                category: '餐饮'
            };
            console.log('OCR识别完成:', mockResult);
            return mockResult;
        }
        catch (error) {
            console.error('OCR识别失败:', error);
            throw new Error('识别失败，请重试');
        }
    }
    /**
     * 语音转文字并解析记账信息
     */
    async parseVoiceInput(audioPath: string): Promise<VoiceParseResult> {
        try {
            console.log('开始语音识别:', audioPath);
            // 模拟语音识别过程
            await this.delay(1500);
            // 模拟识别结果
            const mockResult: VoiceParseResult = {
                text: '今天午餐花了25块5',
                amount: 25.50,
                category: '餐饮',
                description: '午餐',
                type: AccountType.EXPENSE,
                confidence: 0.92
            };
            console.log('语音识别完成:', mockResult);
            return mockResult;
        }
        catch (error) {
            console.error('语音识别失败:', error);
            throw new Error('识别失败，请重试');
        }
    }
    /**
     * 智能分类建议
     */
    async suggestCategory(description: string, amount: number): Promise<CategorySuggestion[]> {
        try {
            console.log('开始智能分类:', description, amount);
            // 模拟AI分析过程
            await this.delay(500);
            // 简单的关键词匹配逻辑
            const suggestions: CategorySuggestion[] = [];
            if (description.includes('餐') || description.includes('饭') || description.includes('咖啡')) {
                suggestions.push({ category: '餐饮', confidence: 0.9 });
            }
            if (description.includes('车') || description.includes('地铁') || description.includes('公交')) {
                suggestions.push({ category: '交通', confidence: 0.85 });
            }
            if (description.includes('买') || description.includes('购') || description.includes('商场')) {
                suggestions.push({ category: '购物', confidence: 0.8 });
            }
            if (amount > 1000) {
                suggestions.push({ category: '住房', confidence: 0.7 });
            }
            // 默认建议
            if (suggestions.length === 0) {
                suggestions.push({ category: '其他', confidence: 0.5 });
            }
            console.log('智能分类完成:', suggestions);
            return suggestions;
        }
        catch (error) {
            console.error('智能分类失败:', error);
            return [{ category: '其他', confidence: 0.5 }];
        }
    }
    /**
     * 消费习惯分析
     */
    async analyzeSpendingHabits(accounts: Account[]): Promise<SpendingAnalysis> {
        try {
            console.log('开始消费分析:', accounts.length);
            // 模拟AI分析过程
            await this.delay(1000);
            const analysis: SpendingAnalysis = {
                totalExpense: accounts
                    .filter(a => a.type === AccountType.EXPENSE)
                    .reduce((sum, a) => sum + a.amount, 0),
                categoryBreakdown: this.calculateCategoryBreakdown(accounts),
                trends: [
                    { period: '本周', amount: 500, change: 15 },
                    { period: '本月', amount: 2000, change: -5 },
                    { period: '本年', amount: 24000, change: 8 }
                ],
                insights: [
                    '您在餐饮方面的支出较上月增加了15%',
                    '建议控制娱乐支出，可节省约200元/月',
                    '您的理财习惯良好，储蓄率达到30%'
                ],
                recommendations: [
                    '建议设置餐饮预算为800元/月',
                    '可以考虑投资一些低风险理财产品',
                    '尝试记录每日支出，提高理财意识'
                ]
            };
            console.log('消费分析完成:', analysis);
            return analysis;
        }
        catch (error) {
            console.error('消费分析失败:', error);
            throw new Error('分析失败，请重试');
        }
    }
    /**
     * 预算建议
     */
    async suggestBudget(accounts: Account[], income: number): Promise<BudgetSuggestion> {
        try {
            console.log('开始预算建议分析');
            await this.delay(800);
            const monthlyExpense = accounts
                .filter(a => a.type === AccountType.EXPENSE)
                .reduce((sum, a) => sum + a.amount, 0);
            const suggestion: BudgetSuggestion = {
                totalBudget: income * 0.7,
                categoryBudgets: [
                    { category: '餐饮', amount: income * 0.15, reason: '基于您的消费习惯' },
                    { category: '交通', amount: income * 0.1, reason: '通勤必需支出' },
                    { category: '购物', amount: income * 0.2, reason: '生活必需品和娱乐' },
                    { category: '住房', amount: income * 0.3, reason: '房租或房贷' }
                ],
                savingsGoal: income * 0.3,
                confidence: 0.88
            };
            console.log('预算建议完成:', suggestion);
            return suggestion;
        }
        catch (error) {
            console.error('预算建议失败:', error);
            throw new Error('分析失败，请重试');
        }
    }
    // 私有方法
    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    private calculateCategoryBreakdown(accounts: Account[]): CategoryBreakdown[] {
        const categoryMap = new Map<string, number>();
        accounts
            .filter(a => a.type === AccountType.EXPENSE)
            .forEach(account => {
            const current = categoryMap.get(account.category) || 0;
            categoryMap.set(account.category, current + account.amount);
        });
        const total = Array.from(categoryMap.values()).reduce((sum, amount) => sum + amount, 0);
        const result: CategoryBreakdown[] = [];
        categoryMap.forEach((amount: number, category: string) => {
            const breakdown: CategoryBreakdown = {
                category: category,
                amount: amount,
                percentage: total > 0 ? (amount / total) * 100 : 0
            };
            result.push(breakdown);
        });
        return result;
    }
}
// 类型定义
export interface ReceiptInfo {
    totalAmount: number;
    merchantName: string;
    items: ReceiptItem[];
    date: Date;
    confidence: number;
    category: string;
}
export interface ReceiptItem {
    name: string;
    price: number;
    quantity: number;
}
export interface VoiceParseResult {
    text: string;
    amount: number;
    category: string;
    description: string;
    type: AccountType;
    confidence: number;
}
export interface CategorySuggestion {
    category: string;
    confidence: number;
}
export interface SpendingAnalysis {
    totalExpense: number;
    categoryBreakdown: CategoryBreakdown[];
    trends: TrendData[];
    insights: string[];
    recommendations: string[];
}
export interface CategoryBreakdown {
    category: string;
    amount: number;
    percentage: number;
}
export interface TrendData {
    period: string;
    amount: number;
    change: number; // 百分比变化
}
export interface BudgetSuggestion {
    totalBudget: number;
    categoryBudgets: CategoryBudget[];
    savingsGoal: number;
    confidence: number;
}
export interface CategoryBudget {
    category: string;
    amount: number;
    reason: string;
}
