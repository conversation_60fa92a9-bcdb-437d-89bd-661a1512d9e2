if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface SummaryCard_Params {
    summary?: AccountSummary;
    period?: string;
}
import { AccountSummary } from "@normalized:N&&&entry/src/main/ets/models/Account&";
import { FormatUtils } from "@normalized:N&&&entry/src/main/ets/common/utils/FormatUtils&";
export class SummaryCard extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__summary = new SynchedPropertyObjectOneWayPU(params.summary, this, "summary");
        this.__period = new SynchedPropertySimpleOneWayPU(params.period, this, "period");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: SummaryCard_Params) {
        if (params.summary === undefined) {
            this.__summary.set(new AccountSummary([]));
        }
        if (params.period === undefined) {
            this.__period.set('本月');
        }
    }
    updateStateVars(params: SummaryCard_Params) {
        this.__summary.reset(params.summary);
        this.__period.reset(params.period);
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__summary.purgeDependencyOnElmtId(rmElmtId);
        this.__period.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__summary.aboutToBeDeleted();
        this.__period.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __summary: SynchedPropertySimpleOneWayPU<AccountSummary>;
    get summary() {
        return this.__summary.get();
    }
    set summary(newValue: AccountSummary) {
        this.__summary.set(newValue);
    }
    private __period: SynchedPropertySimpleOneWayPU<string>;
    get period() {
        return this.__period.get();
    }
    set period(newValue: string) {
        this.__period.set(newValue);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/components/SummaryCard.ets(13:5)", "entry");
            Column.width('100%');
            Column.padding(20);
            Column.backgroundColor('#FFFFFF');
            Column.borderRadius(12);
            Column.shadow({
                radius: 8,
                color: '#1A000000',
                offsetX: 0,
                offsetY: 4
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 标题
            Row.create();
            Row.debugLine("entry/src/main/ets/components/SummaryCard.ets(15:7)", "entry");
            // 标题
            Row.width('100%');
            // 标题
            Row.margin({ bottom: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`${this.period}统计`);
            Text.debugLine("entry/src/main/ets/components/SummaryCard.ets(16:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/components/SummaryCard.ets(21:9)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`共${this.summary.accountCount}笔`);
            Text.debugLine("entry/src/main/ets/components/SummaryCard.ets(23:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#757575');
        }, Text);
        Text.pop();
        // 标题
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 余额
            Column.create();
            Column.debugLine("entry/src/main/ets/components/SummaryCard.ets(31:7)", "entry");
            // 余额
            Column.alignItems(HorizontalAlign.Center);
            // 余额
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('余额');
            Text.debugLine("entry/src/main/ets/components/SummaryCard.ets(32:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#757575');
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(FormatUtils.formatAmount(this.summary.balance));
            Text.debugLine("entry/src/main/ets/components/SummaryCard.ets(37:9)", "entry");
            Text.fontSize(28);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor(this.summary.balance >= 0 ? '#4CAF50' : '#F44336');
        }, Text);
        Text.pop();
        // 余额
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 收支统计
            Row.create();
            Row.debugLine("entry/src/main/ets/components/SummaryCard.ets(46:7)", "entry");
            // 收支统计
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 收入
            Column.create();
            Column.debugLine("entry/src/main/ets/components/SummaryCard.ets(48:9)", "entry");
            // 收入
            Column.layoutWeight(1);
            // 收入
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/components/SummaryCard.ets(49:11)", "entry");
            Row.margin({ bottom: 4 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📈');
            Text.debugLine("entry/src/main/ets/components/SummaryCard.ets(50:13)", "entry");
            Text.fontSize(16);
            Text.margin({ right: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('收入');
            Text.debugLine("entry/src/main/ets/components/SummaryCard.ets(54:13)", "entry");
            Text.fontSize(14);
            Text.fontColor('#757575');
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(FormatUtils.formatAmount(this.summary.totalIncome));
            Text.debugLine("entry/src/main/ets/components/SummaryCard.ets(60:11)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#4CAF50');
        }, Text);
        Text.pop();
        // 收入
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 分隔线
            Line.create();
            Line.debugLine("entry/src/main/ets/components/SummaryCard.ets(69:9)", "entry");
            // 分隔线
            Line.width(1);
            // 分隔线
            Line.height(40);
            // 分隔线
            Line.backgroundColor('#E0E0E0');
        }, Line);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 支出
            Column.create();
            Column.debugLine("entry/src/main/ets/components/SummaryCard.ets(75:9)", "entry");
            // 支出
            Column.layoutWeight(1);
            // 支出
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/components/SummaryCard.ets(76:11)", "entry");
            Row.margin({ bottom: 4 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📉');
            Text.debugLine("entry/src/main/ets/components/SummaryCard.ets(77:13)", "entry");
            Text.fontSize(16);
            Text.margin({ right: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支出');
            Text.debugLine("entry/src/main/ets/components/SummaryCard.ets(81:13)", "entry");
            Text.fontSize(14);
            Text.fontColor('#757575');
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(FormatUtils.formatAmount(this.summary.totalExpense));
            Text.debugLine("entry/src/main/ets/components/SummaryCard.ets(87:11)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#F44336');
        }, Text);
        Text.pop();
        // 支出
        Column.pop();
        // 收支统计
        Row.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
}
