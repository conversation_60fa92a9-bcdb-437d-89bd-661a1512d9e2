import { Account, AccountType } from '../models/Account';
import { AIService, ReceiptInfo, ReceiptItem } from '../services/AIService';
import { PermissionService, PermissionStatus } from '../services/PermissionService';
import router from '@ohos.router';
import camera from '@ohos.multimedia.camera';

@Entry
@Component
struct PhotoScanPage {
  @State isScanning: boolean = false;
  @State scanResult: ReceiptInfo | null = null;
  @State showResult: boolean = false;
  @State errorMessage: string = '';
  @State capturedImage: string = '';
  @State hasPermission: boolean = false;
  @State showPermissionDialog: boolean = false;

  private aiService: AIService = AIService.getInstance();
  private permissionService: PermissionService = PermissionService.getInstance();

  aboutToAppear() {
    this.checkCameraPermission();
  }

  build() {
    Column() {
      // 顶部导航栏
      this.buildHeader()

      if (!this.hasPermission) {
        // 权限请求界面
        this.buildPermissionView()
      } else if (!this.showResult) {
        // 拍照界面
        this.buildCameraView()
      } else {
        // 识别结果界面
        this.buildResultView()
      }

      // 权限说明对话框
      if (this.showPermissionDialog) {
        this.buildPermissionDialog()
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#000000')
  }

  @Builder
  buildHeader() {
    Row() {
      // 返回按钮
      Row() {
        Text('←')
          .fontSize(20)
          .fontColor('#FFFFFF')
      }
      .width(40)
      .height(40)
      .borderRadius(20)
      .justifyContent(FlexAlign.Center)
      .alignItems(VerticalAlign.Center)
      .onClick(() => {
        router.back();
      })

      Text('拍照识别')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .fontColor('#FFFFFF')
        .layoutWeight(1)
        .textAlign(TextAlign.Center)

      // 帮助按钮
      Row() {
        Text('?')
          .fontSize(16)
          .fontColor('#FFFFFF')
      }
      .width(40)
      .height(40)
      .borderRadius(20)
      .justifyContent(FlexAlign.Center)
      .alignItems(VerticalAlign.Center)
      .onClick(() => {
        this.showHelpDialog();
      })
    }
    .width('100%')
    .padding({ left: 16, right: 16, top: 12, bottom: 12 })
  }

  @Builder
  buildPermissionView() {
    Column() {
      Text('📷')
        .fontSize(80)
        .fontColor('#FFFFFF')
        .margin({ bottom: 20 })

      Text('需要相机权限')
        .fontSize(20)
        .fontWeight(FontWeight.Bold)
        .fontColor('#FFFFFF')
        .margin({ bottom: 12 })

      Text('拍照识别功能需要使用相机权限来拍摄小票')
        .fontSize(16)
        .fontColor('#CCCCCC')
        .textAlign(TextAlign.Center)
        .margin({ bottom: 40 })

      Button('授权相机权限')
        .width(200)
        .height(48)
        .fontSize(16)
        .backgroundColor('#1976D2')
        .borderRadius(24)
        .onClick(() => {
          this.requestCameraPermission();
        })

      Text('了解更多')
        .fontSize(14)
        .fontColor('#1976D2')
        .margin({ top: 20 })
        .onClick(() => {
          this.showPermissionDialog = true;
        })
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .padding(40)
  }

  @Builder
  buildPermissionDialog() {
    Column() {
      Column() {
        Text('相机权限说明')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .fontColor('#212121')
          .margin({ bottom: 16 })

        Text('MonterAI需要相机权限来实现以下功能：')
          .fontSize(14)
          .fontColor('#757575')
          .margin({ bottom: 12 })

        Column() {
          Text('• 拍摄小票和发票')
            .fontSize(14)
            .fontColor('#212121')
            .alignSelf(ItemAlign.Start)
            .margin({ bottom: 8 })

          Text('• AI识别消费信息')
            .fontSize(14)
            .fontColor('#212121')
            .alignSelf(ItemAlign.Start)
            .margin({ bottom: 8 })

          Text('• 自动记录账单数据')
            .fontSize(14)
            .fontColor('#212121')
            .alignSelf(ItemAlign.Start)
        }
        .width('100%')
        .margin({ bottom: 20 })

        Text('我们承诺不会在未经您同意的情况下使用相机功能')
          .fontSize(12)
          .fontColor('#9E9E9E')
          .textAlign(TextAlign.Center)
          .margin({ bottom: 24 })

        Row() {
          Button('取消')
            .width('45%')
            .height(40)
            .fontSize(14)
            .backgroundColor('#E0E0E0')
            .fontColor('#212121')
            .borderRadius(8)
            .onClick(() => {
              this.showPermissionDialog = false;
            })

          Button('授权')
            .width('45%')
            .height(40)
            .fontSize(14)
            .backgroundColor('#1976D2')
            .borderRadius(8)
            .onClick(() => {
              this.showPermissionDialog = false;
              this.requestCameraPermission();
            })
        }
        .width('100%')
        .justifyContent(FlexAlign.SpaceBetween)
      }
      .width('85%')
      .padding(24)
      .backgroundColor('#FFFFFF')
      .borderRadius(12)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#80000000')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .onClick(() => {
      this.showPermissionDialog = false;
    })
  }

  @Builder
  buildCameraView() {
    Column() {
      // 相机预览区域
      Stack() {
        // 这里应该是相机预览组件，暂时用占位符
        Column() {
          Text('📷')
            .fontSize(80)
            .fontColor('#FFFFFF')
            .margin({ bottom: 16 })
          
          Text('将小票对准取景框')
            .fontSize(16)
            .fontColor('#FFFFFF')
            .textAlign(TextAlign.Center)
        }
        .width('100%')
        .height('100%')
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)

        // 取景框
        Column() {
          Row()
            .width(200)
            .height(2)
            .backgroundColor('#00FF00')
          
          Row()
            .width(2)
            .height(300)
            .backgroundColor('#00FF00')
            .margin({ top: -2 })
          
          Row()
            .width(200)
            .height(2)
            .backgroundColor('#00FF00')
            .margin({ top: -2 })
        }
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)

        // 扫描状态提示
        if (this.isScanning) {
          Column() {
            Text('正在识别中...')
              .fontSize(16)
              .fontColor('#FFFFFF')
              .backgroundColor('#80000000')
              .padding(12)
              .borderRadius(8)
          }
          .width('100%')
          .height('100%')
          .justifyContent(FlexAlign.Center)
          .alignItems(HorizontalAlign.Center)
        }
      }
      .layoutWeight(1)

      // 底部操作栏
      Row() {
        // 相册按钮
        Column() {
          Text('🖼️')
            .fontSize(24)
            .margin({ bottom: 4 })
          
          Text('相册')
            .fontSize(12)
            .fontColor('#FFFFFF')
        }
        .width(60)
        .alignItems(HorizontalAlign.Center)
        .onClick(() => {
          this.selectFromGallery();
        })

        Blank()

        // 拍照按钮
        Row() {
          Text('📸')
            .fontSize(32)
            .fontColor('#FFFFFF')
        }
        .width(80)
        .height(80)
        .borderRadius(40)
        .backgroundColor('#1976D2')
        .justifyContent(FlexAlign.Center)
        .alignItems(VerticalAlign.Center)
        .enabled(!this.isScanning)
        .onClick(() => {
          this.capturePhoto();
        })

        Blank()

        // 闪光灯按钮
        Column() {
          Text('⚡')
            .fontSize(24)
            .margin({ bottom: 4 })
          
          Text('闪光灯')
            .fontSize(12)
            .fontColor('#FFFFFF')
        }
        .width(60)
        .alignItems(HorizontalAlign.Center)
        .onClick(() => {
          this.toggleFlash();
        })
      }
      .width('100%')
      .padding({ left: 20, right: 20, top: 20, bottom: 40 })
    }
  }

  @Builder
  buildResultView() {
    Column() {
      // 识别结果
      Scroll() {
        Column() {
          if (this.scanResult) {
            // 商家信息
            Text(this.scanResult.merchantName)
              .fontSize(20)
              .fontWeight(FontWeight.Bold)
              .fontColor('#FFFFFF')
              .margin({ bottom: 8 })

            // 总金额
            Text(`¥${this.scanResult.totalAmount.toFixed(2)}`)
              .fontSize(32)
              .fontWeight(FontWeight.Bold)
              .fontColor('#4CAF50')
              .margin({ bottom: 16 })

            // 置信度
            Text(`识别准确度: ${(this.scanResult.confidence * 100).toFixed(0)}%`)
              .fontSize(14)
              .fontColor('#CCCCCC')
              .margin({ bottom: 20 })

            // 商品列表
            if (this.scanResult.items.length > 0) {
              Text('商品明细')
                .fontSize(16)
                .fontWeight(FontWeight.Bold)
                .fontColor('#FFFFFF')
                .alignSelf(ItemAlign.Start)
                .margin({ bottom: 12 })

              ForEach(this.scanResult.items, (item: ReceiptItem, index: number) => {
                Row() {
                  Text(item.name)
                    .fontSize(14)
                    .fontColor('#FFFFFF')
                    .layoutWeight(1)

                  Text(`${item.quantity}x`)
                    .fontSize(14)
                    .fontColor('#CCCCCC')
                    .margin({ right: 8 })

                  Text(`¥${item.price.toFixed(2)}`)
                    .fontSize(14)
                    .fontColor('#FFFFFF')
                }
                .width('100%')
                .padding({ top: 8, bottom: 8 })
                .margin({ bottom: index < this.scanResult!.items.length - 1 ? 4 : 0 })
              })
            }
          }

          if (this.errorMessage) {
            Text(this.errorMessage)
              .fontSize(16)
              .fontColor('#F44336')
              .textAlign(TextAlign.Center)
              .margin({ top: 20 })
          }
        }
        .width('100%')
        .padding(20)
      }
      .layoutWeight(1)

      // 底部按钮
      Row() {
        Button('重新拍照')
          .width('45%')
          .height(48)
          .fontSize(16)
          .backgroundColor('#424242')
          .borderRadius(8)
          .onClick(() => {
            this.resetScan();
          })

        Button('确认记账')
          .width('45%')
          .height(48)
          .fontSize(16)
          .backgroundColor('#1976D2')
          .borderRadius(8)
          .enabled(this.scanResult !== null)
          .onClick(() => {
            this.confirmAndSave();
          })
      }
      .width('100%')
      .padding(20)
      .justifyContent(FlexAlign.SpaceBetween)
    }
  }

  // 权限相关方法
  private async checkCameraPermission(): Promise<void> {
    try {
      const status = await this.permissionService.checkCameraPermission();
      this.hasPermission = status === PermissionStatus.GRANTED;

      if (!this.hasPermission) {
        console.log('相机权限未授权');
      }
    } catch (error) {
      console.error('检查相机权限失败:', error);
      this.hasPermission = false;
    }
  }

  private async requestCameraPermission(): Promise<void> {
    try {
      const result = await this.permissionService.requestCameraPermission();

      if (result.status === PermissionStatus.GRANTED) {
        this.hasPermission = true;
        console.log('相机权限授权成功');
      } else {
        this.hasPermission = false;
        console.log('相机权限被拒绝:', result.message);

        // 可以显示引导用户到设置页面的提示
        if (result.status === PermissionStatus.DENIED) {
          this.showPermissionDeniedTip();
        }
      }
    } catch (error) {
      console.error('请求相机权限失败:', error);
      this.hasPermission = false;
    }
  }

  private showPermissionDeniedTip(): void {
    console.log('显示权限被拒绝提示');
    // TODO: 显示引导用户到设置页面开启权限的提示
  }

  // 事件处理方法
  private async capturePhoto() {
    try {
      this.isScanning = true;
      this.errorMessage = '';

      // 模拟拍照过程
      await this.delay(500);
      this.capturedImage = 'mock_image_path.jpg';

      // 调用AI识别
      this.scanResult = await this.aiService.recognizeReceipt(this.capturedImage);
      this.showResult = true;

    } catch (error) {
      this.errorMessage = '识别失败，请重试';
    } finally {
      this.isScanning = false;
    }
  }

  private selectFromGallery() {
    console.log('从相册选择');
    // TODO: 实现从相册选择图片
  }

  private toggleFlash() {
    console.log('切换闪光灯');
    // TODO: 实现闪光灯切换
  }

  private showHelpDialog() {
    console.log('显示帮助信息');
    // TODO: 显示拍照识别帮助信息
  }

  private resetScan() {
    this.showResult = false;
    this.scanResult = null;
    this.errorMessage = '';
    this.capturedImage = '';
  }

  private confirmAndSave() {
    if (!this.scanResult) return;

    // 创建账单对象
    const account = new Account();
    account.amount = this.scanResult.totalAmount;
    account.type = AccountType.EXPENSE;
    account.category = this.scanResult.category;
    account.description = this.scanResult.merchantName;
    account.date = this.scanResult.date;
    account.isAIGenerated = true;
    account.aiConfidence = this.scanResult.confidence;

    console.log('保存AI识别账单:', account);
    
    // TODO: 保存到数据库
    
    // 返回主页
    router.back();
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
