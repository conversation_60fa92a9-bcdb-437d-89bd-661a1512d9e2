/**
 * 快速操作栏组件
 */
@Component
export struct QuickActionBar {
  onAddExpense?: () => void;
  onAddIncome?: () => void;
  onPhotoScan?: () => void;
  onVoiceInput?: () => void;

  build() {
    Row() {
      // 添加支出
      Column() {
        Row() {
          Text('💸')
            .fontSize(20)
        }
        .width(48)
        .height(48)
        .borderRadius(24)
        .backgroundColor('#F44336')
        .justifyContent(FlexAlign.Center)
        .alignItems(VerticalAlign.Center)
        .margin({ bottom: 8 })

        Text('支出')
          .fontSize(12)
          .fontColor('#212121')
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Center)
      .onClick(() => {
        if (this.onAddExpense) {
          this.onAddExpense();
        }
      })

      // 添加收入
      Column() {
        Row() {
          Text('💰')
            .fontSize(20)
        }
        .width(48)
        .height(48)
        .borderRadius(24)
        .backgroundColor('#4CAF50')
        .justifyContent(FlexAlign.Center)
        .alignItems(VerticalAlign.Center)
        .margin({ bottom: 8 })

        Text('收入')
          .fontSize(12)
          .fontColor('#212121')
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Center)
      .onClick(() => {
        if (this.onAddIncome) {
          this.onAddIncome();
        }
      })

      // 拍照识别
      Column() {
        Row() {
          Text('📷')
            .fontSize(20)
        }
        .width(48)
        .height(48)
        .borderRadius(24)
        .backgroundColor('#2196F3')
        .justifyContent(FlexAlign.Center)
        .alignItems(VerticalAlign.Center)
        .margin({ bottom: 8 })

        Text('拍照')
          .fontSize(12)
          .fontColor('#212121')
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Center)
      .onClick(() => {
        if (this.onPhotoScan) {
          this.onPhotoScan();
        }
      })

      // 语音记账
      Column() {
        Row() {
          Text('🎤')
            .fontSize(20)
        }
        .width(48)
        .height(48)
        .borderRadius(24)
        .backgroundColor('#FF9800')
        .justifyContent(FlexAlign.Center)
        .alignItems(VerticalAlign.Center)
        .margin({ bottom: 8 })

        Text('语音')
          .fontSize(12)
          .fontColor('#212121')
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Center)
      .onClick(() => {
        if (this.onVoiceInput) {
          this.onVoiceInput();
        }
      })
    }
    .width('100%')
    .padding({ left: 20, right: 20, top: 16, bottom: 16 })
    .backgroundColor('#FFFFFF')
    .borderRadius(12)
    .shadow({
      radius: 4,
      color: '#1F000000',
      offsetX: 0,
      offsetY: 2
    })
  }
}
