/**
 * 用户数据接口
 */
export interface UserData {
  id?: string;
  username?: string;
  avatar?: string;
  email?: string;
  phone?: string;
  settings?: UserSettings;
  createdAt?: Date;
  lastLoginAt?: Date;
}

/**
 * 用户设置数据接口
 */
export interface UserSettingsData {
  currency?: string;
  language?: string;
  theme?: ThemeMode;
  defaultExpenseCategory?: string;
  defaultIncomeCategory?: string;
  enableVoiceInput?: boolean;
  enablePhotoRecognition?: boolean;
  monthlyBudget?: number;
  budgetWarningThreshold?: number;
  enableBudgetNotification?: boolean;
  enableAIAnalysis?: boolean;
  enableAIRecommendation?: boolean;
  aiConfidenceThreshold?: number;
  enableCloudSync?: boolean;
  enableDistributedSync?: boolean;
  autoSyncInterval?: number;
  enableBiometricAuth?: boolean;
  enableDataEncryption?: boolean;
  dataRetentionDays?: number;
}

/**
 * 预算数据接口
 */
export interface BudgetData {
  id?: string;
  name?: string;
  amount?: number;
  period?: BudgetPeriod;
  categories?: string[];
  startDate?: Date;
  endDate?: Date;
  isActive?: boolean;
}

/**
 * 用户数据模型
 */
export class User {
  id: string = '';
  username: string = '';
  avatar?: string;
  email?: string;
  phone?: string;
  settings: UserSettings = new UserSettings();
  createdAt: Date = new Date();
  lastLoginAt?: Date;
  
  constructor(data?: UserData) {
    if (data) {
      if (data.id !== undefined) this.id = data.id;
      if (data.username !== undefined) this.username = data.username;
      if (data.avatar !== undefined) this.avatar = data.avatar;
      if (data.email !== undefined) this.email = data.email;
      if (data.phone !== undefined) this.phone = data.phone;
      if (data.settings !== undefined) this.settings = data.settings;
      if (data.createdAt !== undefined) this.createdAt = data.createdAt;
      if (data.lastLoginAt !== undefined) this.lastLoginAt = data.lastLoginAt;
    }
    if (!this.id) {
      this.id = this.generateId();
    }
  }
  
  private generateId(): string {
    return Date.now().toString() + Math.random().toString(36).substring(2, 11);
  }
}

/**
 * 用户设置
 */
export class UserSettings {
  // 通用设置
  currency: string = 'CNY';
  language: string = 'zh-CN';
  theme: ThemeMode = ThemeMode.AUTO;
  
  // 记账设置
  defaultExpenseCategory: string = '';
  defaultIncomeCategory: string = '';
  enableVoiceInput: boolean = true;
  enablePhotoRecognition: boolean = true;
  
  // 预算设置
  monthlyBudget: number = 0;
  budgetWarningThreshold: number = 0.8; // 80%时提醒
  enableBudgetNotification: boolean = true;
  
  // AI设置
  enableAIAnalysis: boolean = true;
  enableAIRecommendation: boolean = true;
  aiConfidenceThreshold: number = 0.7; // AI识别置信度阈值
  
  // 同步设置
  enableCloudSync: boolean = false;
  enableDistributedSync: boolean = true;
  autoSyncInterval: number = 300; // 5分钟
  
  // 隐私设置
  enableBiometricAuth: boolean = false;
  enableDataEncryption: boolean = true;
  dataRetentionDays: number = 365; // 数据保留天数
  
  constructor(data?: UserSettingsData) {
    if (data) {
      if (data.currency !== undefined) this.currency = data.currency;
      if (data.language !== undefined) this.language = data.language;
      if (data.theme !== undefined) this.theme = data.theme;
      if (data.defaultExpenseCategory !== undefined) this.defaultExpenseCategory = data.defaultExpenseCategory;
      if (data.defaultIncomeCategory !== undefined) this.defaultIncomeCategory = data.defaultIncomeCategory;
      if (data.enableVoiceInput !== undefined) this.enableVoiceInput = data.enableVoiceInput;
      if (data.enablePhotoRecognition !== undefined) this.enablePhotoRecognition = data.enablePhotoRecognition;
      if (data.monthlyBudget !== undefined) this.monthlyBudget = data.monthlyBudget;
      if (data.budgetWarningThreshold !== undefined) this.budgetWarningThreshold = data.budgetWarningThreshold;
      if (data.enableBudgetNotification !== undefined) this.enableBudgetNotification = data.enableBudgetNotification;
      if (data.enableAIAnalysis !== undefined) this.enableAIAnalysis = data.enableAIAnalysis;
      if (data.enableAIRecommendation !== undefined) this.enableAIRecommendation = data.enableAIRecommendation;
      if (data.aiConfidenceThreshold !== undefined) this.aiConfidenceThreshold = data.aiConfidenceThreshold;
      if (data.enableCloudSync !== undefined) this.enableCloudSync = data.enableCloudSync;
      if (data.enableDistributedSync !== undefined) this.enableDistributedSync = data.enableDistributedSync;
      if (data.autoSyncInterval !== undefined) this.autoSyncInterval = data.autoSyncInterval;
      if (data.enableBiometricAuth !== undefined) this.enableBiometricAuth = data.enableBiometricAuth;
      if (data.enableDataEncryption !== undefined) this.enableDataEncryption = data.enableDataEncryption;
      if (data.dataRetentionDays !== undefined) this.dataRetentionDays = data.dataRetentionDays;
    }
  }
}

/**
 * 主题模式枚举
 */
export enum ThemeMode {
  LIGHT = 'light',
  DARK = 'dark',
  AUTO = 'auto'
}

/**
 * 预算配置
 */
export class Budget {
  id: string = '';
  name: string = '';
  amount: number = 0;
  period: BudgetPeriod = BudgetPeriod.MONTHLY;
  categories: string[] = []; // 关联的分类ID
  startDate: Date = new Date();
  endDate?: Date;
  isActive: boolean = true;
  
  constructor(data?: BudgetData) {
    if (data) {
      if (data.id !== undefined) this.id = data.id;
      if (data.name !== undefined) this.name = data.name;
      if (data.amount !== undefined) this.amount = data.amount;
      if (data.period !== undefined) this.period = data.period;
      if (data.categories !== undefined) this.categories = data.categories;
      if (data.startDate !== undefined) this.startDate = data.startDate;
      if (data.endDate !== undefined) this.endDate = data.endDate;
      if (data.isActive !== undefined) this.isActive = data.isActive;
    }
    if (!this.id) {
      this.id = this.generateId();
    }
  }
  
  private generateId(): string {
    return Date.now().toString() + Math.random().toString(36).substring(2, 11);
  }
  
  /**
   * 获取预算进度百分比
   */
  getProgress(usedAmount: number): number {
    return Math.min((usedAmount / this.amount) * 100, 100);
  }
  
  /**
   * 检查是否超出预算
   */
  isOverBudget(usedAmount: number): boolean {
    return usedAmount > this.amount;
  }
}

/**
 * 预算周期枚举
 */
export enum BudgetPeriod {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  YEARLY = 'yearly'
}
