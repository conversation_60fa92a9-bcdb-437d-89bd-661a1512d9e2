if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface ProgressRing_Params {
    progress?: number;
    size?: number;
    strokeWidth?: number;
    color?: string;
    backgroundColor?: string;
    showText?: boolean;
}
interface SimplePieChart_Params {
    data?: ChartData[];
    size?: number;
}
interface PieChart_Params {
    data?: ChartData[];
    size?: number;
    showLabels?: boolean;
    showLegend?: boolean;
}
export class PieChart extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__data = new SynchedPropertyObjectOneWayPU(params.data, this, "data");
        this.__size = new SynchedPropertySimpleOneWayPU(params.size, this, "size");
        this.__showLabels = new SynchedPropertySimpleOneWayPU(params.showLabels, this, "showLabels");
        this.__showLegend = new SynchedPropertySimpleOneWayPU(params.showLegend, this, "showLegend");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: PieChart_Params) {
        if (params.size === undefined) {
            this.__size.set(200);
        }
        if (params.showLabels === undefined) {
            this.__showLabels.set(true);
        }
        if (params.showLegend === undefined) {
            this.__showLegend.set(true);
        }
    }
    updateStateVars(params: PieChart_Params) {
        this.__data.reset(params.data);
        this.__size.reset(params.size);
        this.__showLabels.reset(params.showLabels);
        this.__showLegend.reset(params.showLegend);
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__data.purgeDependencyOnElmtId(rmElmtId);
        this.__size.purgeDependencyOnElmtId(rmElmtId);
        this.__showLabels.purgeDependencyOnElmtId(rmElmtId);
        this.__showLegend.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__data.aboutToBeDeleted();
        this.__size.aboutToBeDeleted();
        this.__showLabels.aboutToBeDeleted();
        this.__showLegend.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __data: SynchedPropertySimpleOneWayPU<ChartData[]>;
    get data() {
        return this.__data.get();
    }
    set data(newValue: ChartData[]) {
        this.__data.set(newValue);
    }
    private __size: SynchedPropertySimpleOneWayPU<number>;
    get size() {
        return this.__size.get();
    }
    set size(newValue: number) {
        this.__size.set(newValue);
    }
    private __showLabels: SynchedPropertySimpleOneWayPU<boolean>;
    get showLabels() {
        return this.__showLabels.get();
    }
    set showLabels(newValue: boolean) {
        this.__showLabels.set(newValue);
    }
    private __showLegend: SynchedPropertySimpleOneWayPU<boolean>;
    get showLegend() {
        return this.__showLegend.get();
    }
    set showLegend(newValue: boolean) {
        this.__showLegend.set(newValue);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/components/PieChart.ets(12:5)", "entry");
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 图表区域
            Stack.create();
            Stack.debugLine("entry/src/main/ets/components/PieChart.ets(14:7)", "entry");
            // 图表区域
            Stack.width(this.size);
            // 图表区域
            Stack.height(this.size);
        }, Stack);
        // 饼图绘制
        this.buildPieChart.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 中心文字
            if (this.data.length > 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.buildCenterText.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        // 图表区域
        Stack.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 图例
            if (this.showLegend) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.buildLegend.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    buildPieChart(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Canvas.create(this.getCanvasRenderingContext());
            Canvas.debugLine("entry/src/main/ets/components/PieChart.ets(36:5)", "entry");
            Canvas.width(this.size);
            Canvas.height(this.size);
            Canvas.onReady(() => {
                this.drawPieChart();
            });
        }, Canvas);
        Canvas.pop();
    }
    buildCenterText(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/components/PieChart.ets(46:5)", "entry");
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('总计');
            Text.debugLine("entry/src/main/ets/components/PieChart.ets(47:7)", "entry");
            Text.fontSize(12);
            Text.fontColor('#757575');
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getTotalAmount());
            Text.debugLine("entry/src/main/ets/components/PieChart.ets(52:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
        }, Text);
        Text.pop();
        Column.pop();
    }
    buildLegend(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/components/PieChart.ets(63:5)", "entry");
            Column.width('100%');
            Column.margin({ top: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = (_item, index: number) => {
                const item = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Row.create();
                    Row.debugLine("entry/src/main/ets/components/PieChart.ets(65:9)", "entry");
                    Row.width('100%');
                    Row.padding({ top: 8, bottom: 8 });
                    Row.margin({ bottom: index < this.data.length - 1 ? 4 : 0 });
                }, Row);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    // 颜色指示器
                    Row.create();
                    Row.debugLine("entry/src/main/ets/components/PieChart.ets(67:11)", "entry");
                    // 颜色指示器
                    Row.width(12);
                    // 颜色指示器
                    Row.height(12);
                    // 颜色指示器
                    Row.backgroundColor(item.color);
                    // 颜色指示器
                    Row.borderRadius(6);
                    // 颜色指示器
                    Row.margin({ right: 8 });
                }, Row);
                // 颜色指示器
                Row.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    // 标签
                    Text.create(item.label);
                    Text.debugLine("entry/src/main/ets/components/PieChart.ets(75:11)", "entry");
                    // 标签
                    Text.fontSize(14);
                    // 标签
                    Text.fontColor('#212121');
                    // 标签
                    Text.layoutWeight(1);
                }, Text);
                // 标签
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    // 数值
                    Text.create(item.value.toFixed(2));
                    Text.debugLine("entry/src/main/ets/components/PieChart.ets(81:11)", "entry");
                    // 数值
                    Text.fontSize(14);
                    // 数值
                    Text.fontColor('#757575');
                    // 数值
                    Text.margin({ right: 8 });
                }, Text);
                // 数值
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    // 百分比
                    Text.create(`${item.percentage.toFixed(1)}%`);
                    Text.debugLine("entry/src/main/ets/components/PieChart.ets(87:11)", "entry");
                    // 百分比
                    Text.fontSize(12);
                    // 百分比
                    Text.fontColor('#9E9E9E');
                }, Text);
                // 百分比
                Text.pop();
                Row.pop();
            };
            this.forEachUpdateFunction(elmtId, this.data, forEachItemGenFunction, undefined, true, false);
        }, ForEach);
        ForEach.pop();
        Column.pop();
    }
    // 私有方法
    private getCanvasRenderingContext(): CanvasRenderingContext2D {
        // 这里需要返回Canvas的渲染上下文
        // 在实际实现中，需要通过Canvas组件的onReady回调获取
        return {} as CanvasRenderingContext2D;
    }
    private drawPieChart(): void {
        // 简化的饼图绘制逻辑
        // 在实际实现中，这里会使用Canvas API绘制饼图
        console.log('绘制饼图:', this.data);
        // TODO: 实现真实的Canvas绘制逻辑
        // 1. 计算每个扇形的角度
        // 2. 使用arc()方法绘制扇形
        // 3. 填充颜色
        // 4. 绘制标签（如果需要）
    }
    private getTotalAmount(): string {
        const total = this.data.reduce((sum, item) => sum + item.value, 0);
        return `¥${total.toFixed(2)}`;
    }
    rerender() {
        this.updateDirtyElements();
    }
}
export class SimplePieChart extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__data = new SynchedPropertyObjectOneWayPU(params.data, this, "data");
        this.__size = new SynchedPropertySimpleOneWayPU(params.size, this, "size");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: SimplePieChart_Params) {
        if (params.size === undefined) {
            this.__size.set(200);
        }
    }
    updateStateVars(params: SimplePieChart_Params) {
        this.__data.reset(params.data);
        this.__size.reset(params.size);
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__data.purgeDependencyOnElmtId(rmElmtId);
        this.__size.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__data.aboutToBeDeleted();
        this.__size.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __data: SynchedPropertySimpleOneWayPU<ChartData[]>;
    get data() {
        return this.__data.get();
    }
    set data(newValue: ChartData[]) {
        this.__data.set(newValue);
    }
    private __size: SynchedPropertySimpleOneWayPU<number>;
    get size() {
        return this.__size.get();
    }
    set size(newValue: number) {
        this.__size.set(newValue);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/components/PieChart.ets(134:5)", "entry");
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 使用堆叠的圆形和遮罩来模拟饼图
            Stack.create();
            Stack.debugLine("entry/src/main/ets/components/PieChart.ets(136:7)", "entry");
            // 使用堆叠的圆形和遮罩来模拟饼图
            Stack.width(this.size);
            // 使用堆叠的圆形和遮罩来模拟饼图
            Stack.height(this.size);
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 背景圆
            Row.create();
            Row.debugLine("entry/src/main/ets/components/PieChart.ets(138:9)", "entry");
            // 背景圆
            Row.width(this.size);
            // 背景圆
            Row.height(this.size);
            // 背景圆
            Row.borderRadius(this.size / 2);
            // 背景圆
            Row.backgroundColor('#E0E0E0');
        }, Row);
        // 背景圆
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 数据扇形（简化实现）
            ForEach.create();
            const forEachItemGenFunction = (_item, index: number) => {
                const item = _item;
                this.buildSegment.bind(this)(item, index);
            };
            this.forEachUpdateFunction(elmtId, this.data, forEachItemGenFunction, undefined, true, false);
        }, ForEach);
        // 数据扇形（简化实现）
        ForEach.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 中心圆
            Row.create();
            Row.debugLine("entry/src/main/ets/components/PieChart.ets(150:9)", "entry");
            // 中心圆
            Row.width(this.size * 0.6);
            // 中心圆
            Row.height(this.size * 0.6);
            // 中心圆
            Row.borderRadius(this.size * 0.3);
            // 中心圆
            Row.backgroundColor('#FFFFFF');
        }, Row);
        // 中心圆
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 中心文字
            Column.create();
            Column.debugLine("entry/src/main/ets/components/PieChart.ets(157:9)", "entry");
            // 中心文字
            Column.justifyContent(FlexAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('总支出');
            Text.debugLine("entry/src/main/ets/components/PieChart.ets(158:11)", "entry");
            Text.fontSize(12);
            Text.fontColor('#757575');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getTotalAmount());
            Text.debugLine("entry/src/main/ets/components/PieChart.ets(162:11)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 中心文字
        Column.pop();
        // 使用堆叠的圆形和遮罩来模拟饼图
        Stack.pop();
        // 图例
        this.buildSimpleLegend.bind(this)();
        Column.pop();
    }
    buildSegment(item: ChartData, index: number, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 简化的扇形实现 - 使用边框来模拟
            Row.create();
            Row.debugLine("entry/src/main/ets/components/PieChart.ets(182:5)", "entry");
            // 简化的扇形实现 - 使用边框来模拟
            Row.width(this.size);
            // 简化的扇形实现 - 使用边框来模拟
            Row.height(this.size);
            // 简化的扇形实现 - 使用边框来模拟
            Row.borderRadius(this.size / 2);
            // 简化的扇形实现 - 使用边框来模拟
            Row.border({
                width: 20,
                color: item.color,
                style: BorderStyle.Solid
            });
            // 简化的扇形实现 - 使用边框来模拟
            Row.rotate({
                angle: index * 60,
                centerX: '50%',
                centerY: '50%'
            });
        }, Row);
        // 简化的扇形实现 - 使用边框来模拟
        Row.pop();
    }
    buildSimpleLegend(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/components/PieChart.ets(200:5)", "entry");
            Column.width('100%');
            Column.margin({ top: 16 });
            Column.padding({ left: 16, right: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = (_item, index: number) => {
                const item = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Row.create();
                    Row.debugLine("entry/src/main/ets/components/PieChart.ets(202:9)", "entry");
                    Row.width('100%');
                    Row.padding({ top: 6, bottom: 6 });
                }, Row);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Row.create();
                    Row.debugLine("entry/src/main/ets/components/PieChart.ets(203:11)", "entry");
                    Row.width(12);
                    Row.height(12);
                    Row.backgroundColor(item.color);
                    Row.borderRadius(2);
                    Row.margin({ right: 8 });
                }, Row);
                Row.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(item.label);
                    Text.debugLine("entry/src/main/ets/components/PieChart.ets(210:11)", "entry");
                    Text.fontSize(14);
                    Text.fontColor('#212121');
                    Text.layoutWeight(1);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(`¥${item.value.toFixed(2)}`);
                    Text.debugLine("entry/src/main/ets/components/PieChart.ets(215:11)", "entry");
                    Text.fontSize(14);
                    Text.fontColor('#757575');
                }, Text);
                Text.pop();
                Row.pop();
            };
            this.forEachUpdateFunction(elmtId, this.data, forEachItemGenFunction, undefined, true, false);
        }, ForEach);
        ForEach.pop();
        Column.pop();
    }
    private getTotalAmount(): string {
        const total = this.data.reduce((sum, item) => sum + item.value, 0);
        return total.toFixed(2);
    }
    rerender() {
        this.updateDirtyElements();
    }
}
export class ProgressRing extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__progress = new SynchedPropertySimpleOneWayPU(params.progress, this, "progress");
        this.__size = new SynchedPropertySimpleOneWayPU(params.size, this, "size");
        this.__strokeWidth = new SynchedPropertySimpleOneWayPU(params.strokeWidth, this, "strokeWidth");
        this.__color = new SynchedPropertySimpleOneWayPU(params.color, this, "color");
        this.__backgroundColor = new SynchedPropertySimpleOneWayPU(params.backgroundColor, this, "backgroundColor");
        this.__showText = new SynchedPropertySimpleOneWayPU(params.showText, this, "showText");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: ProgressRing_Params) {
        if (params.progress === undefined) {
            this.__progress.set(0);
        }
        if (params.size === undefined) {
            this.__size.set(120);
        }
        if (params.strokeWidth === undefined) {
            this.__strokeWidth.set(8);
        }
        if (params.color === undefined) {
            this.__color.set('#1976D2');
        }
        if (params.backgroundColor === undefined) {
            this.__backgroundColor.set('#E0E0E0');
        }
        if (params.showText === undefined) {
            this.__showText.set(true);
        }
    }
    updateStateVars(params: ProgressRing_Params) {
        this.__progress.reset(params.progress);
        this.__size.reset(params.size);
        this.__strokeWidth.reset(params.strokeWidth);
        this.__color.reset(params.color);
        this.__backgroundColor.reset(params.backgroundColor);
        this.__showText.reset(params.showText);
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__progress.purgeDependencyOnElmtId(rmElmtId);
        this.__size.purgeDependencyOnElmtId(rmElmtId);
        this.__strokeWidth.purgeDependencyOnElmtId(rmElmtId);
        this.__color.purgeDependencyOnElmtId(rmElmtId);
        this.__backgroundColor.purgeDependencyOnElmtId(rmElmtId);
        this.__showText.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__progress.aboutToBeDeleted();
        this.__size.aboutToBeDeleted();
        this.__strokeWidth.aboutToBeDeleted();
        this.__color.aboutToBeDeleted();
        this.__backgroundColor.aboutToBeDeleted();
        this.__showText.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __progress: SynchedPropertySimpleOneWayPU<number>; // 0-100
    get progress() {
        return this.__progress.get();
    }
    set progress(newValue: number) {
        this.__progress.set(newValue);
    }
    private __size: SynchedPropertySimpleOneWayPU<number>;
    get size() {
        return this.__size.get();
    }
    set size(newValue: number) {
        this.__size.set(newValue);
    }
    private __strokeWidth: SynchedPropertySimpleOneWayPU<number>;
    get strokeWidth() {
        return this.__strokeWidth.get();
    }
    set strokeWidth(newValue: number) {
        this.__strokeWidth.set(newValue);
    }
    private __color: SynchedPropertySimpleOneWayPU<string>;
    get color() {
        return this.__color.get();
    }
    set color(newValue: string) {
        this.__color.set(newValue);
    }
    private __backgroundColor: SynchedPropertySimpleOneWayPU<string>;
    get backgroundColor() {
        return this.__backgroundColor.get();
    }
    set backgroundColor(newValue: string) {
        this.__backgroundColor.set(newValue);
    }
    private __showText: SynchedPropertySimpleOneWayPU<boolean>;
    get showText() {
        return this.__showText.get();
    }
    set showText(newValue: boolean) {
        this.__showText.set(newValue);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create();
            Stack.debugLine("entry/src/main/ets/components/PieChart.ets(247:5)", "entry");
            Stack.width(this.size);
            Stack.height(this.size);
            Stack.justifyContent(FlexAlign.Center);
            Stack.alignItems(VerticalAlign.Center);
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 背景环
            Row.create();
            Row.debugLine("entry/src/main/ets/components/PieChart.ets(249:7)", "entry");
            // 背景环
            Row.width(this.size);
            // 背景环
            Row.height(this.size);
            // 背景环
            Row.borderRadius(this.size / 2);
            // 背景环
            Row.border({
                width: this.strokeWidth,
                color: this.backgroundColor,
                style: BorderStyle.Solid
            });
        }, Row);
        // 背景环
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 进度环（简化实现）
            Row.create();
            Row.debugLine("entry/src/main/ets/components/PieChart.ets(260:7)", "entry");
            // 进度环（简化实现）
            Row.width(this.size);
            // 进度环（简化实现）
            Row.height(this.size);
            // 进度环（简化实现）
            Row.borderRadius(this.size / 2);
            // 进度环（简化实现）
            Row.border({
                width: this.strokeWidth,
                color: this.color,
                style: BorderStyle.Solid
            });
            // 进度环（简化实现）
            Row.clip(true);
            // 进度环（简化实现）
            Row.rotate({
                angle: -90 + (this.progress * 3.6),
                centerX: '50%',
                centerY: '50%'
            });
        }, Row);
        // 进度环（简化实现）
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 中心文字
            if (this.showText) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`${this.progress.toFixed(0)}%`);
                        Text.debugLine("entry/src/main/ets/components/PieChart.ets(278:9)", "entry");
                        Text.fontSize(16);
                        Text.fontWeight(FontWeight.Bold);
                        Text.fontColor('#212121');
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Stack.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
}
// 图表数据接口
export interface ChartData {
    label: string;
    value: number;
    color: string;
    percentage: number;
}
// 图表工具类
export class ChartUtils {
    /**
     * 计算百分比
     */
    static calculatePercentages(data: Omit<ChartData, 'percentage'>[]): ChartData[] {
        const total = data.reduce((sum, item) => sum + item.value, 0);
        return data.map(item => ({
            ...item,
            percentage: total > 0 ? (item.value / total) * 100 : 0
        }));
    }
    /**
     * 生成默认颜色
     */
    static getDefaultColors(): string[] {
        return [
            '#FF9800',
            '#2196F3',
            '#E91E63',
            '#4CAF50',
            '#9C27B0',
            '#FF5722',
            '#607D8B',
            '#795548' // 棕色
        ];
    }
    /**
     * 为数据分配颜色
     */
    static assignColors(data: Omit<ChartData, 'color' | 'percentage'>[]): Omit<ChartData, 'percentage'>[] {
        const colors = ChartUtils.getDefaultColors();
        return data.map((item, index) => ({
            ...item,
            color: colors[index % colors.length]
        }));
    }
}
