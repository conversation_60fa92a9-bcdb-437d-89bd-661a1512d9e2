if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface VoiceInputPage_Params {
    isRecording?: boolean;
    isProcessing?: boolean;
    recordingTime?: number;
    voiceText?: string;
    parseResult?: VoiceParseResult | null;
    showResult?: boolean;
    errorMessage?: string;
    hasPermission?: boolean;
    aiService?: AIService;
    permissionService?: PermissionService;
    recordingTimer?: number;
}
import { Account, AccountType } from "@normalized:N&&&entry/src/main/ets/models/Account&";
import { AIService } from "@normalized:N&&&entry/src/main/ets/services/AIService&";
import type { VoiceParseResult } from "@normalized:N&&&entry/src/main/ets/services/AIService&";
import { PermissionService, PermissionStatus } from "@normalized:N&&&entry/src/main/ets/services/PermissionService&";
import router from "@ohos:router";
class VoiceInputPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__isRecording = new ObservedPropertySimplePU(false, this, "isRecording");
        this.__isProcessing = new ObservedPropertySimplePU(false, this, "isProcessing");
        this.__recordingTime = new ObservedPropertySimplePU(0, this, "recordingTime");
        this.__voiceText = new ObservedPropertySimplePU('', this, "voiceText");
        this.__parseResult = new ObservedPropertyObjectPU(null, this, "parseResult");
        this.__showResult = new ObservedPropertySimplePU(false, this, "showResult");
        this.__errorMessage = new ObservedPropertySimplePU('', this, "errorMessage");
        this.__hasPermission = new ObservedPropertySimplePU(false, this, "hasPermission");
        this.aiService = AIService.getInstance();
        this.permissionService = PermissionService.getInstance();
        this.recordingTimer = -1;
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: VoiceInputPage_Params) {
        if (params.isRecording !== undefined) {
            this.isRecording = params.isRecording;
        }
        if (params.isProcessing !== undefined) {
            this.isProcessing = params.isProcessing;
        }
        if (params.recordingTime !== undefined) {
            this.recordingTime = params.recordingTime;
        }
        if (params.voiceText !== undefined) {
            this.voiceText = params.voiceText;
        }
        if (params.parseResult !== undefined) {
            this.parseResult = params.parseResult;
        }
        if (params.showResult !== undefined) {
            this.showResult = params.showResult;
        }
        if (params.errorMessage !== undefined) {
            this.errorMessage = params.errorMessage;
        }
        if (params.hasPermission !== undefined) {
            this.hasPermission = params.hasPermission;
        }
        if (params.aiService !== undefined) {
            this.aiService = params.aiService;
        }
        if (params.permissionService !== undefined) {
            this.permissionService = params.permissionService;
        }
        if (params.recordingTimer !== undefined) {
            this.recordingTimer = params.recordingTimer;
        }
    }
    updateStateVars(params: VoiceInputPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__isRecording.purgeDependencyOnElmtId(rmElmtId);
        this.__isProcessing.purgeDependencyOnElmtId(rmElmtId);
        this.__recordingTime.purgeDependencyOnElmtId(rmElmtId);
        this.__voiceText.purgeDependencyOnElmtId(rmElmtId);
        this.__parseResult.purgeDependencyOnElmtId(rmElmtId);
        this.__showResult.purgeDependencyOnElmtId(rmElmtId);
        this.__errorMessage.purgeDependencyOnElmtId(rmElmtId);
        this.__hasPermission.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__isRecording.aboutToBeDeleted();
        this.__isProcessing.aboutToBeDeleted();
        this.__recordingTime.aboutToBeDeleted();
        this.__voiceText.aboutToBeDeleted();
        this.__parseResult.aboutToBeDeleted();
        this.__showResult.aboutToBeDeleted();
        this.__errorMessage.aboutToBeDeleted();
        this.__hasPermission.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __isRecording: ObservedPropertySimplePU<boolean>;
    get isRecording() {
        return this.__isRecording.get();
    }
    set isRecording(newValue: boolean) {
        this.__isRecording.set(newValue);
    }
    private __isProcessing: ObservedPropertySimplePU<boolean>;
    get isProcessing() {
        return this.__isProcessing.get();
    }
    set isProcessing(newValue: boolean) {
        this.__isProcessing.set(newValue);
    }
    private __recordingTime: ObservedPropertySimplePU<number>;
    get recordingTime() {
        return this.__recordingTime.get();
    }
    set recordingTime(newValue: number) {
        this.__recordingTime.set(newValue);
    }
    private __voiceText: ObservedPropertySimplePU<string>;
    get voiceText() {
        return this.__voiceText.get();
    }
    set voiceText(newValue: string) {
        this.__voiceText.set(newValue);
    }
    private __parseResult: ObservedPropertyObjectPU<VoiceParseResult | null>;
    get parseResult() {
        return this.__parseResult.get();
    }
    set parseResult(newValue: VoiceParseResult | null) {
        this.__parseResult.set(newValue);
    }
    private __showResult: ObservedPropertySimplePU<boolean>;
    get showResult() {
        return this.__showResult.get();
    }
    set showResult(newValue: boolean) {
        this.__showResult.set(newValue);
    }
    private __errorMessage: ObservedPropertySimplePU<string>;
    get errorMessage() {
        return this.__errorMessage.get();
    }
    set errorMessage(newValue: string) {
        this.__errorMessage.set(newValue);
    }
    private __hasPermission: ObservedPropertySimplePU<boolean>;
    get hasPermission() {
        return this.__hasPermission.get();
    }
    set hasPermission(newValue: boolean) {
        this.__hasPermission.set(newValue);
    }
    private aiService: AIService;
    private permissionService: PermissionService;
    private recordingTimer: number;
    aboutToAppear() {
        this.checkMicrophonePermission();
    }
    aboutToDisappear() {
        if (this.recordingTimer !== -1) {
            clearInterval(this.recordingTimer);
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(33:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        // 顶部导航栏
        this.buildHeader.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (!this.showResult) {
                this.ifElseBranchUpdateFunction(0, () => {
                    // 语音录制界面
                    this.buildRecordingView.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    // 识别结果界面
                    this.buildResultView.bind(this)();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    buildHeader(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(52:5)", "entry");
            Row.width('100%');
            Row.padding({ left: 16, right: 16, top: 12, bottom: 12 });
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 返回按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(54:7)", "entry");
            // 返回按钮
            Row.width(40);
            // 返回按钮
            Row.height(40);
            // 返回按钮
            Row.borderRadius(20);
            // 返回按钮
            Row.justifyContent(FlexAlign.Center);
            // 返回按钮
            Row.alignItems(VerticalAlign.Center);
            // 返回按钮
            Row.onClick(() => {
                router.back();
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('←');
            Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(55:9)", "entry");
            Text.fontSize(20);
            Text.fontColor('#212121');
        }, Text);
        Text.pop();
        // 返回按钮
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('语音记账');
            Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(68:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 帮助按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(76:7)", "entry");
            // 帮助按钮
            Row.width(40);
            // 帮助按钮
            Row.height(40);
            // 帮助按钮
            Row.borderRadius(20);
            // 帮助按钮
            Row.justifyContent(FlexAlign.Center);
            // 帮助按钮
            Row.alignItems(VerticalAlign.Center);
            // 帮助按钮
            Row.onClick(() => {
                this.showHelpDialog();
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('?');
            Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(77:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#212121');
        }, Text);
        Text.pop();
        // 帮助按钮
        Row.pop();
        Row.pop();
    }
    buildRecordingView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(97:5)", "entry");
            Column.width('100%');
            Column.padding(20);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 提示信息
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(99:7)", "entry");
            // 提示信息
            Column.layoutWeight(1);
            // 提示信息
            Column.justifyContent(FlexAlign.Center);
            // 提示信息
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🎤');
            Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(100:9)", "entry");
            Text.fontSize(80);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isProcessing) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('正在识别中...');
                        Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(105:11)", "entry");
                        Text.fontSize(18);
                        Text.fontColor('#1976D2');
                        Text.margin({ bottom: 8 });
                    }, Text);
                    Text.pop();
                });
            }
            else if (this.isRecording) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('正在录音...');
                        Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(110:11)", "entry");
                        Text.fontSize(18);
                        Text.fontColor('#F44336');
                        Text.margin({ bottom: 8 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.formatTime(this.recordingTime));
                        Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(115:11)", "entry");
                        Text.fontSize(24);
                        Text.fontWeight(FontWeight.Bold);
                        Text.fontColor('#F44336');
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('点击按钮开始录音');
                        Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(120:11)", "entry");
                        Text.fontSize(18);
                        Text.fontColor('#757575');
                        Text.margin({ bottom: 8 });
                    }, Text);
                    Text.pop();
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 语音示例
            if (!this.isRecording && !this.isProcessing) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(128:11)", "entry");
                        Column.margin({ top: 20 });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('语音示例：');
                        Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(129:13)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#9E9E9E');
                        Text.margin({ bottom: 8 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('"今天午餐花了25块5"');
                        Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(134:13)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#9E9E9E');
                        Text.margin({ bottom: 4 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('"买菜花了50元"');
                        Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(139:13)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#9E9E9E');
                        Text.margin({ bottom: 4 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('"收到工资8000元"');
                        Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(144:13)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#9E9E9E');
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        // 提示信息
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 录音按钮
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(156:7)", "entry");
            // 录音按钮
            Column.margin({ bottom: 40 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(157:9)", "entry");
            Row.width(120);
            Row.height(120);
            Row.borderRadius(60);
            Row.backgroundColor(this.getRecordButtonColor());
            Row.justifyContent(FlexAlign.Center);
            Row.alignItems(VerticalAlign.Center);
            Row.enabled(!this.isProcessing);
            Row.onClick(() => {
                if (this.isRecording) {
                    this.stopRecording();
                }
                else {
                    this.startRecording();
                }
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isRecording) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('⏹️');
                        Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(159:13)", "entry");
                        Text.fontSize(40);
                        Text.fontColor('#FFFFFF');
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('🎤');
                        Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(163:13)", "entry");
                        Text.fontSize(40);
                        Text.fontColor('#FFFFFF');
                    }, Text);
                    Text.pop();
                });
            }
        }, If);
        If.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getRecordButtonText());
            Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(183:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#757575');
            Text.margin({ top: 12 });
        }, Text);
        Text.pop();
        // 录音按钮
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 错误信息
            if (this.errorMessage) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.errorMessage);
                        Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(192:9)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#F44336');
                        Text.textAlign(TextAlign.Center);
                        Text.margin({ bottom: 20 });
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    buildResultView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(205:5)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 识别结果
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(207:7)", "entry");
            // 识别结果
            Scroll.layoutWeight(1);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(208:9)", "entry");
            Column.width('100%');
            Column.padding(20);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.parseResult) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 识别的文字
                        Text.create('识别结果');
                        Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(211:13)", "entry");
                        // 识别的文字
                        Text.fontSize(16);
                        // 识别的文字
                        Text.fontWeight(FontWeight.Bold);
                        // 识别的文字
                        Text.fontColor('#212121');
                        // 识别的文字
                        Text.alignSelf(ItemAlign.Start);
                        // 识别的文字
                        Text.margin({ bottom: 8 });
                    }, Text);
                    // 识别的文字
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`"${this.parseResult.text}"`);
                        Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(218:13)", "entry");
                        Text.fontSize(18);
                        Text.fontColor('#1976D2');
                        Text.backgroundColor('#E3F2FD');
                        Text.padding(16);
                        Text.borderRadius(8);
                        Text.margin({ bottom: 20 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 解析结果
                        Text.create('解析信息');
                        Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(227:13)", "entry");
                        // 解析结果
                        Text.fontSize(16);
                        // 解析结果
                        Text.fontWeight(FontWeight.Bold);
                        // 解析结果
                        Text.fontColor('#212121');
                        // 解析结果
                        Text.alignSelf(ItemAlign.Start);
                        // 解析结果
                        Text.margin({ bottom: 12 });
                    }, Text);
                    // 解析结果
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(234:13)", "entry");
                        Column.width('100%');
                        Column.backgroundColor('#FFFFFF');
                        Column.padding(16);
                        Column.borderRadius(8);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(235:15)", "entry");
                        Row.width('100%');
                        Row.margin({ bottom: 8 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('类型:');
                        Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(236:17)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#757575');
                        Text.width(60);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.parseResult.type === AccountType.INCOME ? '收入' : '支出');
                        Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(241:17)", "entry");
                        Text.fontSize(14);
                        Text.fontColor(this.parseResult.type === AccountType.INCOME ? '#4CAF50' : '#F44336');
                        Text.fontWeight(FontWeight.Medium);
                    }, Text);
                    Text.pop();
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(249:15)", "entry");
                        Row.width('100%');
                        Row.margin({ bottom: 8 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('金额:');
                        Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(250:17)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#757575');
                        Text.width(60);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`¥${this.parseResult.amount.toFixed(2)}`);
                        Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(255:17)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#212121');
                        Text.fontWeight(FontWeight.Medium);
                    }, Text);
                    Text.pop();
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(263:15)", "entry");
                        Row.width('100%');
                        Row.margin({ bottom: 8 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('分类:');
                        Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(264:17)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#757575');
                        Text.width(60);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.parseResult.category);
                        Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(269:17)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#212121');
                        Text.fontWeight(FontWeight.Medium);
                    }, Text);
                    Text.pop();
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.parseResult.description) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Row.create();
                                    Row.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(278:17)", "entry");
                                    Row.width('100%');
                                    Row.margin({ bottom: 8 });
                                }, Row);
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create('备注:');
                                    Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(279:19)", "entry");
                                    Text.fontSize(14);
                                    Text.fontColor('#757575');
                                    Text.width(60);
                                }, Text);
                                Text.pop();
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create(this.parseResult.description);
                                    Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(284:19)", "entry");
                                    Text.fontSize(14);
                                    Text.fontColor('#212121');
                                    Text.fontWeight(FontWeight.Medium);
                                }, Text);
                                Text.pop();
                                Row.pop();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(293:15)", "entry");
                        Row.width('100%');
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('准确度:');
                        Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(294:17)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#757575');
                        Text.width(60);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`${(this.parseResult.confidence * 100).toFixed(0)}%`);
                        Text.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(299:17)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#4CAF50');
                        Text.fontWeight(FontWeight.Medium);
                    }, Text);
                    Text.pop();
                    Row.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
        // 识别结果
        Scroll.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 底部按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(318:7)", "entry");
            // 底部按钮
            Row.width('100%');
            // 底部按钮
            Row.padding(20);
            // 底部按钮
            Row.justifyContent(FlexAlign.SpaceBetween);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('重新录音');
            Button.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(319:9)", "entry");
            Button.width('45%');
            Button.height(48);
            Button.fontSize(16);
            Button.backgroundColor('#9E9E9E');
            Button.borderRadius(8);
            Button.onClick(() => {
                this.resetRecording();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('确认记账');
            Button.debugLine("entry/src/main/ets/pages/VoiceInputPage.ets(329:9)", "entry");
            Button.width('45%');
            Button.height(48);
            Button.fontSize(16);
            Button.backgroundColor('#1976D2');
            Button.borderRadius(8);
            Button.enabled(this.parseResult !== null);
            Button.onClick(() => {
                this.confirmAndSave();
            });
        }, Button);
        Button.pop();
        // 底部按钮
        Row.pop();
        Column.pop();
    }
    // 事件处理方法
    private async startRecording(): Promise<void> {
        // 检查权限
        if (!this.hasPermission) {
            await this.requestMicrophonePermission();
            if (!this.hasPermission) {
                this.errorMessage = '需要麦克风权限才能进行语音记账';
                return;
            }
        }
        this.isRecording = true;
        this.recordingTime = 0;
        this.errorMessage = '';
        // 开始计时
        this.recordingTimer = setInterval(() => {
            this.recordingTime++;
            if (this.recordingTime >= 60) { // 最长60秒
                this.stopRecording();
            }
        }, 1000);
        console.log('开始录音');
        // TODO: 启动语音录制
    }
    private async stopRecording() {
        this.isRecording = false;
        this.isProcessing = true;
        if (this.recordingTimer !== -1) {
            clearInterval(this.recordingTimer);
            this.recordingTimer = -1;
        }
        try {
            console.log('停止录音，开始处理');
            // 模拟语音文件路径
            const audioPath = 'mock_audio_path.wav';
            // 调用AI识别
            this.parseResult = await this.aiService.parseVoiceInput(audioPath);
            this.showResult = true;
        }
        catch (error) {
            this.errorMessage = error.message || '识别失败，请重试';
        }
        finally {
            this.isProcessing = false;
        }
    }
    private resetRecording() {
        this.showResult = false;
        this.parseResult = null;
        this.errorMessage = '';
        this.recordingTime = 0;
        this.voiceText = '';
    }
    private confirmAndSave() {
        if (!this.parseResult)
            return;
        // 创建账单对象
        const account = new Account();
        account.amount = this.parseResult.amount;
        account.type = this.parseResult.type;
        account.category = this.parseResult.category;
        account.description = this.parseResult.description;
        account.date = new Date();
        account.isAIGenerated = true;
        account.aiConfidence = this.parseResult.confidence;
        console.log('保存语音识别账单:', account);
        // TODO: 保存到数据库
        // 返回主页
        router.back();
    }
    private showHelpDialog() {
        console.log('显示语音记账帮助');
        // TODO: 显示语音记账帮助信息
    }
    // 辅助方法
    private getRecordButtonColor(): string {
        if (this.isProcessing)
            return '#9E9E9E';
        if (this.isRecording)
            return '#F44336';
        return '#1976D2';
    }
    private getRecordButtonText(): string {
        if (this.isProcessing)
            return '处理中...';
        if (this.isRecording)
            return '点击停止';
        return '点击录音';
    }
    private formatTime(seconds: number): string {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    // 权限相关方法
    private async checkMicrophonePermission(): Promise<void> {
        try {
            const status = await this.permissionService.checkMicrophonePermission();
            this.hasPermission = status === PermissionStatus.GRANTED;
            if (!this.hasPermission) {
                console.log('麦克风权限未授权');
            }
        }
        catch (error) {
            console.error('检查麦克风权限失败:', error);
            this.hasPermission = false;
        }
    }
    private async requestMicrophonePermission(): Promise<void> {
        try {
            const result = await this.permissionService.requestMicrophonePermission();
            if (result.status === PermissionStatus.GRANTED) {
                this.hasPermission = true;
                console.log('麦克风权限授权成功');
            }
            else {
                this.hasPermission = false;
                console.log('麦克风权限被拒绝:', result.message);
            }
        }
        catch (error) {
            console.error('请求麦克风权限失败:', error);
            this.hasPermission = false;
        }
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "VoiceInputPage";
    }
}
registerNamedRoute(() => new VoiceInputPage(undefined, {}), "", { bundleName: "com.silence.monter", moduleName: "entry", pagePath: "pages/VoiceInputPage", pageFullPath: "entry/src/main/ets/pages/VoiceInputPage", integratedHsp: "false", moduleType: "followWithHap" });
