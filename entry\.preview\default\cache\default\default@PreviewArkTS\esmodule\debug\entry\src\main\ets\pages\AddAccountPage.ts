if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface AddAccountPage_Params {
    account?: Account;
    categories?: Category[];
    selectedCategory?: Category | null;
    amount?: string;
    description?: string;
    accountType?: AccountType;
    showDatePicker?: boolean;
    isEdit?: boolean;
    isSaving?: boolean;
    databaseService?: DatabaseService;
}
import { Account, AccountType } from "@normalized:N&&&entry/src/main/ets/models/Account&";
import { DefaultCategories } from "@normalized:N&&&entry/src/main/ets/models/Category&";
import type { Category } from "@normalized:N&&&entry/src/main/ets/models/Category&";
import { DatabaseService } from "@normalized:N&&&entry/src/main/ets/services/DatabaseService&";
import router from "@ohos:router";
class AddAccountPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__account = new ObservedPropertyObjectPU(new Account(), this, "account");
        this.__categories = new ObservedPropertyObjectPU([], this, "categories");
        this.__selectedCategory = new ObservedPropertyObjectPU(null, this, "selectedCategory");
        this.__amount = new ObservedPropertySimplePU('', this, "amount");
        this.__description = new ObservedPropertySimplePU('', this, "description");
        this.__accountType = new ObservedPropertySimplePU(AccountType.EXPENSE, this, "accountType");
        this.__showDatePicker = new ObservedPropertySimplePU(false, this, "showDatePicker");
        this.__isEdit = new ObservedPropertySimplePU(false, this, "isEdit");
        this.__isSaving = new ObservedPropertySimplePU(false, this, "isSaving");
        this.databaseService = DatabaseService.getInstance();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: AddAccountPage_Params) {
        if (params.account !== undefined) {
            this.account = params.account;
        }
        if (params.categories !== undefined) {
            this.categories = params.categories;
        }
        if (params.selectedCategory !== undefined) {
            this.selectedCategory = params.selectedCategory;
        }
        if (params.amount !== undefined) {
            this.amount = params.amount;
        }
        if (params.description !== undefined) {
            this.description = params.description;
        }
        if (params.accountType !== undefined) {
            this.accountType = params.accountType;
        }
        if (params.showDatePicker !== undefined) {
            this.showDatePicker = params.showDatePicker;
        }
        if (params.isEdit !== undefined) {
            this.isEdit = params.isEdit;
        }
        if (params.isSaving !== undefined) {
            this.isSaving = params.isSaving;
        }
        if (params.databaseService !== undefined) {
            this.databaseService = params.databaseService;
        }
    }
    updateStateVars(params: AddAccountPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__account.purgeDependencyOnElmtId(rmElmtId);
        this.__categories.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedCategory.purgeDependencyOnElmtId(rmElmtId);
        this.__amount.purgeDependencyOnElmtId(rmElmtId);
        this.__description.purgeDependencyOnElmtId(rmElmtId);
        this.__accountType.purgeDependencyOnElmtId(rmElmtId);
        this.__showDatePicker.purgeDependencyOnElmtId(rmElmtId);
        this.__isEdit.purgeDependencyOnElmtId(rmElmtId);
        this.__isSaving.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__account.aboutToBeDeleted();
        this.__categories.aboutToBeDeleted();
        this.__selectedCategory.aboutToBeDeleted();
        this.__amount.aboutToBeDeleted();
        this.__description.aboutToBeDeleted();
        this.__accountType.aboutToBeDeleted();
        this.__showDatePicker.aboutToBeDeleted();
        this.__isEdit.aboutToBeDeleted();
        this.__isSaving.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __account: ObservedPropertyObjectPU<Account>;
    get account() {
        return this.__account.get();
    }
    set account(newValue: Account) {
        this.__account.set(newValue);
    }
    private __categories: ObservedPropertyObjectPU<Category[]>;
    get categories() {
        return this.__categories.get();
    }
    set categories(newValue: Category[]) {
        this.__categories.set(newValue);
    }
    private __selectedCategory: ObservedPropertyObjectPU<Category | null>;
    get selectedCategory() {
        return this.__selectedCategory.get();
    }
    set selectedCategory(newValue: Category | null) {
        this.__selectedCategory.set(newValue);
    }
    private __amount: ObservedPropertySimplePU<string>;
    get amount() {
        return this.__amount.get();
    }
    set amount(newValue: string) {
        this.__amount.set(newValue);
    }
    private __description: ObservedPropertySimplePU<string>;
    get description() {
        return this.__description.get();
    }
    set description(newValue: string) {
        this.__description.set(newValue);
    }
    private __accountType: ObservedPropertySimplePU<AccountType>;
    get accountType() {
        return this.__accountType.get();
    }
    set accountType(newValue: AccountType) {
        this.__accountType.set(newValue);
    }
    private __showDatePicker: ObservedPropertySimplePU<boolean>;
    get showDatePicker() {
        return this.__showDatePicker.get();
    }
    set showDatePicker(newValue: boolean) {
        this.__showDatePicker.set(newValue);
    }
    private __isEdit: ObservedPropertySimplePU<boolean>;
    get isEdit() {
        return this.__isEdit.get();
    }
    set isEdit(newValue: boolean) {
        this.__isEdit.set(newValue);
    }
    private __isSaving: ObservedPropertySimplePU<boolean>;
    get isSaving() {
        return this.__isSaving.get();
    }
    set isSaving(newValue: boolean) {
        this.__isSaving.set(newValue);
    }
    private databaseService: DatabaseService;
    aboutToAppear() {
        // 获取路由参数
        const params = router.getParams() as Record<string, Object>;
        if (params && params['type']) {
            this.accountType = params['type'] as AccountType;
        }
        // 检查是否为编辑模式
        if (params && params['account']) {
            this.isEdit = true;
            const editAccount = params['account'] as Account;
            this.loadAccountForEdit(editAccount);
        }
        this.loadCategories();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(40:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        // 顶部导航栏
        this.buildHeader.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 内容区域
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(45:7)", "entry");
            // 内容区域
            Scroll.layoutWeight(1);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(46:9)", "entry");
            Column.padding(16);
        }, Column);
        // 账单类型切换
        this.buildTypeSelector.bind(this)();
        // 金额输入
        this.buildAmountInput.bind(this)();
        // 分类选择
        this.buildCategorySelector.bind(this)();
        // 描述输入
        this.buildDescriptionInput.bind(this)();
        // 日期选择
        this.buildDateSelector.bind(this)();
        // 保存按钮
        this.buildSaveButton.bind(this)();
        Column.pop();
        // 内容区域
        Scroll.pop();
        Column.pop();
    }
    buildHeader(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(76:5)", "entry");
            Row.width('100%');
            Row.padding({ left: 16, right: 16, top: 12, bottom: 12 });
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 返回按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(78:7)", "entry");
            // 返回按钮
            Row.width(40);
            // 返回按钮
            Row.height(40);
            // 返回按钮
            Row.borderRadius(20);
            // 返回按钮
            Row.justifyContent(FlexAlign.Center);
            // 返回按钮
            Row.alignItems(VerticalAlign.Center);
            // 返回按钮
            Row.onClick(() => {
                router.back();
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('←');
            Text.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(79:9)", "entry");
            Text.fontSize(20);
            Text.fontColor('#212121');
        }, Text);
        Text.pop();
        // 返回按钮
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getPageTitle());
            Text.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(92:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 占位
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(100:7)", "entry");
            // 占位
            Row.width(40);
            // 占位
            Row.height(40);
        }, Row);
        // 占位
        Row.pop();
        Row.pop();
    }
    buildTypeSelector(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(111:5)", "entry");
            Row.width('100%');
            Row.padding(4);
            Row.backgroundColor('#F0F0F0');
            Row.borderRadius(26);
            Row.margin({ bottom: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 支出
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(113:7)", "entry");
            // 支出
            Row.layoutWeight(1);
            // 支出
            Row.height(44);
            // 支出
            Row.borderRadius(22);
            // 支出
            Row.backgroundColor(this.accountType === AccountType.EXPENSE ? '#F44336' : '#FFFFFF');
            // 支出
            Row.justifyContent(FlexAlign.Center);
            // 支出
            Row.alignItems(VerticalAlign.Center);
            // 支出
            Row.onClick(() => {
                this.accountType = AccountType.EXPENSE;
                this.loadCategories();
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支出');
            Text.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(114:9)", "entry");
            Text.fontSize(16);
            Text.fontColor(this.accountType === AccountType.EXPENSE ? '#FFFFFF' : '#212121');
        }, Text);
        Text.pop();
        // 支出
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 收入
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(130:7)", "entry");
            // 收入
            Row.layoutWeight(1);
            // 收入
            Row.height(44);
            // 收入
            Row.borderRadius(22);
            // 收入
            Row.backgroundColor(this.accountType === AccountType.INCOME ? '#4CAF50' : '#FFFFFF');
            // 收入
            Row.justifyContent(FlexAlign.Center);
            // 收入
            Row.alignItems(VerticalAlign.Center);
            // 收入
            Row.onClick(() => {
                this.accountType = AccountType.INCOME;
                this.loadCategories();
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('收入');
            Text.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(131:9)", "entry");
            Text.fontSize(16);
            Text.fontColor(this.accountType === AccountType.INCOME ? '#FFFFFF' : '#212121');
        }, Text);
        Text.pop();
        // 收入
        Row.pop();
        Row.pop();
    }
    buildAmountInput(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(155:5)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('金额');
            Text.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(156:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入金额', text: this.amount });
            TextInput.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(163:7)", "entry");
            TextInput.fontSize(18);
            TextInput.fontWeight(FontWeight.Bold);
            TextInput.type(InputType.Number);
            TextInput.backgroundColor('#FFFFFF');
            TextInput.borderRadius(8);
            TextInput.padding(16);
            TextInput.onChange((value: string) => {
                this.amount = value;
            });
        }, TextInput);
        Column.pop();
    }
    buildCategorySelector(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(180:5)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('分类');
            Text.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(181:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 分类网格
            Grid.create();
            Grid.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(189:7)", "entry");
            // 分类网格
            Grid.columnsTemplate('1fr 1fr 1fr 1fr');
            // 分类网格
            Grid.rowsGap(8);
            // 分类网格
            Grid.columnsGap(8);
            // 分类网格
            Grid.height(180);
        }, Grid);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const category = _item;
                {
                    const itemCreation2 = (elmtId, isInitialRender) => {
                        GridItem.create(() => { }, false);
                        GridItem.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(191:11)", "entry");
                    };
                    const observedDeepRender = () => {
                        this.observeComponentCreation2(itemCreation2, GridItem);
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Column.create();
                            Column.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(192:13)", "entry");
                            Column.width('100%');
                            Column.height(80);
                            Column.borderRadius(8);
                            Column.backgroundColor(this.selectedCategory?.id === category.id ? category.color : '#FFFFFF');
                            Column.justifyContent(FlexAlign.Center);
                            Column.alignItems(HorizontalAlign.Center);
                            Column.onClick(() => {
                                this.selectedCategory = category;
                            });
                        }, Column);
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Text.create(category.icon);
                            Text.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(193:15)", "entry");
                            Text.fontSize(24);
                            Text.margin({ bottom: 4 });
                        }, Text);
                        Text.pop();
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Text.create(category.name);
                            Text.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(197:15)", "entry");
                            Text.fontSize(12);
                            Text.fontColor('#212121');
                            Text.maxLines(1);
                            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
                        }, Text);
                        Text.pop();
                        Column.pop();
                        GridItem.pop();
                    };
                    observedDeepRender();
                }
            };
            this.forEachUpdateFunction(elmtId, this.categories, forEachItemGenFunction);
        }, ForEach);
        ForEach.pop();
        // 分类网格
        Grid.pop();
        Column.pop();
    }
    buildDescriptionInput(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(226:5)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('备注');
            Text.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(227:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入备注信息（可选）', text: this.description });
            TextInput.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(234:7)", "entry");
            TextInput.fontSize(16);
            TextInput.backgroundColor('#FFFFFF');
            TextInput.borderRadius(8);
            TextInput.padding(16);
            TextInput.onChange((value: string) => {
                this.description = value;
            });
        }, TextInput);
        Column.pop();
    }
    buildDateSelector(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(249:5)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 30 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('日期');
            Text.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(250:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(257:7)", "entry");
            Row.width('100%');
            Row.height(48);
            Row.backgroundColor('#FFFFFF');
            Row.borderRadius(8);
            Row.padding({ left: 16, right: 16 });
            Row.justifyContent(FlexAlign.SpaceBetween);
            Row.alignItems(VerticalAlign.Center);
            Row.onClick(() => {
                this.showDatePicker = true;
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.formatDate(this.account.date));
            Text.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(258:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#212121');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📅');
            Text.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(263:9)", "entry");
            Text.fontSize(16);
        }, Text);
        Text.pop();
        Row.pop();
        Column.pop();
    }
    buildSaveButton(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel(this.isSaving ? '保存中...' : (this.isEdit ? '更新' : '保存'));
            Button.debugLine("entry/src/main/ets/pages/AddAccountPage.ets(283:5)", "entry");
            Button.width('100%');
            Button.height(48);
            Button.fontSize(16);
            Button.fontWeight(FontWeight.Bold);
            Button.backgroundColor(this.canSave() && !this.isSaving ? '#1976D2' : '#CCCCCC');
            Button.borderRadius(8);
            Button.enabled(this.canSave() && !this.isSaving);
            Button.onClick(() => {
                this.saveAccount();
            });
        }, Button);
        Button.pop();
    }
    // 私有方法
    private loadCategories() {
        if (this.accountType === AccountType.EXPENSE) {
            this.categories = DefaultCategories.getExpenseCategories();
        }
        else {
            this.categories = DefaultCategories.getIncomeCategories();
        }
        this.selectedCategory = null;
    }
    private canSave(): boolean {
        return this.amount.length > 0 &&
            parseFloat(this.amount) > 0 &&
            this.selectedCategory !== null;
    }
    private async saveAccount(): Promise<void> {
        if (!this.canSave() || this.isSaving)
            return;
        this.isSaving = true;
        try {
            this.account.amount = parseFloat(this.amount);
            this.account.type = this.accountType;
            this.account.category = this.selectedCategory!.name;
            this.account.description = this.description;
            console.log('保存账单:', this.account);
            const success = await this.databaseService.saveAccount(this.account);
            if (success) {
                console.log('账单保存成功');
                router.back();
            }
            else {
                console.error('账单保存失败');
                // TODO: 显示错误提示
            }
        }
        catch (error) {
            console.error('保存账单时出错:', error);
            // TODO: 显示错误提示
        }
        finally {
            this.isSaving = false;
        }
    }
    private formatDate(date: Date): string {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
    private loadAccountForEdit(editAccount: Account): void {
        this.account = editAccount;
        this.amount = editAccount.amount.toString();
        this.description = editAccount.description || '';
        this.accountType = editAccount.type;
        // 查找对应的分类
        const categories = this.accountType === AccountType.EXPENSE ?
            DefaultCategories.getExpenseCategories() :
            DefaultCategories.getIncomeCategories();
        this.selectedCategory = categories.find(cat => cat.name === editAccount.category) || null;
    }
    private getPageTitle(): string {
        if (this.isEdit) {
            return this.accountType === AccountType.EXPENSE ? '编辑支出' : '编辑收入';
        }
        else {
            return this.accountType === AccountType.EXPENSE ? '添加支出' : '添加收入';
        }
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "AddAccountPage";
    }
}
registerNamedRoute(() => new AddAccountPage(undefined, {}), "", { bundleName: "com.silence.monter", moduleName: "entry", pagePath: "pages/AddAccountPage", pageFullPath: "entry/src/main/ets/pages/AddAccountPage", integratedHsp: "false", moduleType: "followWithHap" });
