import { Account, AccountType } from '../models/Account';
import { AccountCard } from '../components/AccountCard';
import { DatabaseService } from '../services/DatabaseService';
import router from '@ohos.router';

@Entry
@Component
struct AccountListPage {
  @State accounts: Account[] = [];
  @State isLoading: boolean = false;
  @State searchText: string = '';
  @State selectedType: string = 'all'; // all, income, expense
  @State sortBy: string = 'date'; // date, amount, category
  @State hasMore: boolean = true;
  @State currentPage: number = 0;
  
  private pageSize: number = 20;
  private databaseService: DatabaseService = DatabaseService.getInstance();

  aboutToAppear() {
    this.loadAccounts(true);
  }

  build() {
    Column() {
      // 顶部导航栏
      this.buildHeader()

      // 搜索和筛选栏
      this.buildSearchAndFilter()

      // 账单列表
      if (this.isLoading && this.accounts.length === 0) {
        this.buildLoadingView()
      } else if (this.accounts.length === 0) {
        this.buildEmptyView()
      } else {
        this.buildAccountList()
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder
  buildHeader() {
    Row() {
      // 返回按钮
      Row() {
        Text('←')
          .fontSize(20)
          .fontColor('#212121')
      }
      .width(40)
      .height(40)
      .borderRadius(20)
      .justifyContent(FlexAlign.Center)
      .alignItems(VerticalAlign.Center)
      .onClick(() => {
        router.back();
      })

      Text('账单列表')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .fontColor('#212121')
        .layoutWeight(1)
        .textAlign(TextAlign.Center)

      // 添加按钮
      Row() {
        Text('+')
          .fontSize(20)
          .fontColor('#212121')
      }
      .width(40)
      .height(40)
      .borderRadius(20)
      .justifyContent(FlexAlign.Center)
      .alignItems(VerticalAlign.Center)
      .onClick(() => {
        router.pushUrl({
          url: 'pages/AddAccountPage'
        });
      })
    }
    .width('100%')
    .padding({ left: 16, right: 16, top: 12, bottom: 12 })
    .backgroundColor('#FFFFFF')
  }

  @Builder
  buildSearchAndFilter() {
    Column() {
      // 搜索框
      Row() {
        TextInput({ placeholder: '搜索账单...', text: this.searchText })
          .layoutWeight(1)
          .backgroundColor('#F5F5F5')
          .borderRadius(8)
          .onChange((value: string) => {
            this.searchText = value;
            this.debounceSearch();
          })

        Text('🔍')
          .fontSize(16)
          .margin({ left: 8 })
          .onClick(() => {
            this.performSearch();
          })
      }
      .width('100%')
      .margin({ bottom: 12 })

      // 筛选和排序
      Row() {
        // 类型筛选
        Row() {
          Text('类型:')
            .fontSize(14)
            .fontColor('#757575')
            .margin({ right: 8 })

          Text(this.getTypeText())
            .fontSize(14)
            .fontColor('#1976D2')
            .onClick(() => {
              this.showTypeSelector();
            })
        }
        .layoutWeight(1)

        // 排序
        Row() {
          Text('排序:')
            .fontSize(14)
            .fontColor('#757575')
            .margin({ right: 8 })

          Text(this.getSortText())
            .fontSize(14)
            .fontColor('#1976D2')
            .onClick(() => {
              this.showSortSelector();
            })
        }
        .layoutWeight(1)
      }
      .width('100%')
    }
    .width('100%')
    .padding(16)
    .backgroundColor('#FFFFFF')
    .margin({ bottom: 8 })
  }

  @Builder
  buildLoadingView() {
    Column() {
      Text('⏳')
        .fontSize(48)
        .margin({ bottom: 16 })

      Text('加载中...')
        .fontSize(16)
        .fontColor('#757575')
    }
    .width('100%')
    .layoutWeight(1)
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  @Builder
  buildEmptyView() {
    Column() {
      Text('📝')
        .fontSize(48)
        .margin({ bottom: 16 })

      Text('暂无账单记录')
        .fontSize(16)
        .fontColor('#757575')
        .margin({ bottom: 8 })

      Text('点击右上角"+"添加第一笔记录')
        .fontSize(14)
        .fontColor('#9E9E9E')

      Button('立即添加')
        .width(120)
        .height(40)
        .fontSize(14)
        .backgroundColor('#1976D2')
        .borderRadius(20)
        .margin({ top: 20 })
        .onClick(() => {
          router.pushUrl({
            url: 'pages/AddAccountPage'
          });
        })
    }
    .width('100%')
    .layoutWeight(1)
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  @Builder
  buildAccountList() {
    Scroll() {
      Column() {
        ForEach(this.accounts, (account: Account, index: number) => {
          AccountCard({
            account: account,
            showDate: true,
            onItemClick: (account: Account): void => this.handleAccountClick(account),
            onItemLongPress: (account: Account): void => this.handleAccountLongPress(account)
          })
          .margin({ bottom: 8 })
        })

        // 加载更多指示器
        if (this.hasMore) {
          Row() {
            if (this.isLoading) {
              Text('加载中...')
                .fontSize(14)
                .fontColor('#757575')
            } else {
              Text('上拉加载更多')
                .fontSize(14)
                .fontColor('#1976D2')
                .onClick(() => {
                  this.loadMore();
                })
            }
          }
          .width('100%')
          .height(50)
          .justifyContent(FlexAlign.Center)
        } else if (this.accounts.length > 0) {
          Text('没有更多数据了')
            .fontSize(14)
            .fontColor('#9E9E9E')
            .textAlign(TextAlign.Center)
            .width('100%')
            .height(50)
        }
      }
      .padding(16)
    }
    .layoutWeight(1)
    .onReachEnd(() => {
      if (this.hasMore && !this.isLoading) {
        this.loadMore();
      }
    })
  }

  // 事件处理方法
  private async loadAccounts(refresh: boolean = false): Promise<void> {
    if (this.isLoading) return;

    this.isLoading = true;

    try {
      if (refresh) {
        this.currentPage = 0;
        this.accounts = [];
      }

      const offset = this.currentPage * this.pageSize;
      const newAccounts = await this.databaseService.getAccounts(this.pageSize, offset);

      if (refresh) {
        this.accounts = newAccounts;
      } else {
        this.accounts = this.accounts.concat(newAccounts);
      }

      this.hasMore = newAccounts.length === this.pageSize;
      this.currentPage++;

      // 应用筛选和排序
      this.applyFiltersAndSort();

    } catch (error) {
      console.error('加载账单失败:', error);
    } finally {
      this.isLoading = false;
    }
  }

  private loadMore(): void {
    this.loadAccounts(false);
  }

  private handleAccountClick(account: Account): void {
    console.log('点击账单:', account.id);
    router.pushUrl({
      url: 'pages/AccountDetailPage',
      params: {
        accountId: account.id
      }
    });
  }

  private handleAccountLongPress(account: Account): void {
    console.log('长按账单:', account.id);
    // TODO: 显示操作菜单
  }

  private debounceSearch(): void {
    // 简单的防抖实现
    setTimeout(() => {
      this.performSearch();
    }, 500);
  }

  private performSearch(): void {
    console.log('搜索:', this.searchText);
    this.loadAccounts(true);
  }

  private showTypeSelector(): void {
    console.log('显示类型选择器');
    // TODO: 显示类型选择对话框
  }

  private showSortSelector(): void {
    console.log('显示排序选择器');
    // TODO: 显示排序选择对话框
  }

  private applyFiltersAndSort(): void {
    let filteredAccounts = [...this.accounts];

    // 应用类型筛选
    if (this.selectedType !== 'all') {
      filteredAccounts = filteredAccounts.filter(account => 
        account.type === this.selectedType
      );
    }

    // 应用搜索筛选
    if (this.searchText) {
      const searchLower = this.searchText.toLowerCase();
      filteredAccounts = filteredAccounts.filter(account =>
        account.description.toLowerCase().includes(searchLower) ||
        account.category.toLowerCase().includes(searchLower)
      );
    }

    // 应用排序
    filteredAccounts.sort((a, b) => {
      switch (this.sortBy) {
        case 'amount':
          return b.amount - a.amount;
        case 'category':
          return a.category.localeCompare(b.category);
        case 'date':
        default:
          return b.date.getTime() - a.date.getTime();
      }
    });

    this.accounts = filteredAccounts;
  }

  private getTypeText(): string {
    switch (this.selectedType) {
      case 'income': return '收入';
      case 'expense': return '支出';
      default: return '全部';
    }
  }

  private getSortText(): string {
    switch (this.sortBy) {
      case 'amount': return '金额';
      case 'category': return '分类';
      default: return '时间';
    }
  }
}
