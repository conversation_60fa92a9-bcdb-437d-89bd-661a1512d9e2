/**
 * 账单数据模型
 */
export class Account {
  id: string = '';
  amount: number = 0;
  type: AccountType = AccountType.EXPENSE;
  category: string = '';
  description: string = '';
  date: Date = new Date();
  location?: string;
  imageUrl?: string;
  tags: string[] = [];
  isAIGenerated: boolean = false;
  aiConfidence?: number;
  
  constructor(data?: Partial<Account>) {
    if (data) {
      Object.assign(this, data);
    }
    if (!this.id) {
      this.id = this.generateId();
    }
  }
  
  private generateId(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  }
  
  /**
   * 格式化金额显示
   */
  getFormattedAmount(): string {
    const prefix = this.type === AccountType.INCOME ? '+' : '-';
    return `${prefix}¥${this.amount.toFixed(2)}`;
  }
  
  /**
   * 获取账单颜色
   */
  getAmountColor(): string {
    return this.type === AccountType.INCOME ? '#4CAF50' : '#F44336';
  }
}

/**
 * 账单类型枚举
 */
export enum AccountType {
  INCOME = 'income',   // 收入
  EXPENSE = 'expense'  // 支出
}

/**
 * 账单统计数据
 */
export class AccountSummary {
  totalIncome: number = 0;
  totalExpense: number = 0;
  balance: number = 0;
  accountCount: number = 0;
  
  constructor(accounts: Account[]) {
    this.calculateSummary(accounts);
  }
  
  private calculateSummary(accounts: Account[]) {
    this.totalIncome = accounts
      .filter(account => account.type === AccountType.INCOME)
      .reduce((sum, account) => sum + account.amount, 0);
      
    this.totalExpense = accounts
      .filter(account => account.type === AccountType.EXPENSE)
      .reduce((sum, account) => sum + account.amount, 0);
      
    this.balance = this.totalIncome - this.totalExpense;
    this.accountCount = accounts.length;
  }
}
