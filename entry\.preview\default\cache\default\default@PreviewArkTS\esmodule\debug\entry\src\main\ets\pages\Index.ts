if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface Index_Params {
    accounts?: Account[];
    summary?: AccountSummary;
    selectedPeriod?: string;
    isLoading?: boolean;
}
import { AccountSummary } from "@normalized:N&&&entry/src/main/ets/models/Account&";
import type { Account } from "@normalized:N&&&entry/src/main/ets/models/Account&";
import { AccountCard } from "@normalized:N&&&entry/src/main/ets/components/AccountCard&";
import { SummaryCard } from "@normalized:N&&&entry/src/main/ets/components/SummaryCard&";
import { QuickActionBar } from "@normalized:N&&&entry/src/main/ets/components/QuickActionBar&";
class Index extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__accounts = new ObservedPropertyObjectPU([], this, "accounts");
        this.__summary = new ObservedPropertyObjectPU(new AccountSummary([]), this, "summary");
        this.__selectedPeriod = new ObservedPropertySimplePU('本月', this, "selectedPeriod");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: Index_Params) {
        if (params.accounts !== undefined) {
            this.accounts = params.accounts;
        }
        if (params.summary !== undefined) {
            this.summary = params.summary;
        }
        if (params.selectedPeriod !== undefined) {
            this.selectedPeriod = params.selectedPeriod;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
    }
    updateStateVars(params: Index_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__accounts.purgeDependencyOnElmtId(rmElmtId);
        this.__summary.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedPeriod.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__accounts.aboutToBeDeleted();
        this.__summary.aboutToBeDeleted();
        this.__selectedPeriod.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __accounts: ObservedPropertyObjectPU<Account[]>;
    get accounts() {
        return this.__accounts.get();
    }
    set accounts(newValue: Account[]) {
        this.__accounts.set(newValue);
    }
    private __summary: ObservedPropertyObjectPU<AccountSummary>;
    get summary() {
        return this.__summary.get();
    }
    set summary(newValue: AccountSummary) {
        this.__summary.set(newValue);
    }
    private __selectedPeriod: ObservedPropertySimplePU<string>;
    get selectedPeriod() {
        return this.__selectedPeriod.get();
    }
    set selectedPeriod(newValue: string) {
        this.__selectedPeriod.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    aboutToAppear() {
        this.loadMockData();
        this.updateSummary();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/Index.ets(22:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        // 顶部标题栏
        this.buildHeader.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 内容区域
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/Index.ets(27:7)", "entry");
            // 内容区域
            Scroll.layoutWeight(1);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/Index.ets(28:9)", "entry");
            Column.padding({ left: 16, right: 16, bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            __Common__.create();
            __Common__.margin({ bottom: 16 });
        }, __Common__);
        {
            this.observeComponentCreation2((elmtId, isInitialRender) => {
                if (isInitialRender) {
                    let componentCall = new 
                    // 统计卡片
                    SummaryCard(this, {
                        summary: this.summary,
                        period: this.selectedPeriod
                    }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/Index.ets", line: 30, col: 11 });
                    ViewPU.create(componentCall);
                    let paramsLambda = () => {
                        return {
                            summary: this.summary,
                            period: this.selectedPeriod
                        };
                    };
                    componentCall.paramsGenerator_ = paramsLambda;
                }
                else {
                    this.updateStateVarsOfChildByElmtId(elmtId, {
                        summary: this.summary,
                        period: this.selectedPeriod
                    });
                }
            }, { name: "SummaryCard" });
        }
        __Common__.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            __Common__.create();
            __Common__.margin({ bottom: 16 });
        }, __Common__);
        {
            this.observeComponentCreation2((elmtId, isInitialRender) => {
                if (isInitialRender) {
                    let componentCall = new 
                    // 快速操作栏
                    QuickActionBar(this, {
                        onAddExpense: () => this.handleAddExpense(),
                        onAddIncome: () => this.handleAddIncome(),
                        onPhotoScan: () => this.handlePhotoScan(),
                        onVoiceInput: () => this.handleVoiceInput()
                    }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/Index.ets", line: 37, col: 11 });
                    ViewPU.create(componentCall);
                    let paramsLambda = () => {
                        return {
                            onAddExpense: () => this.handleAddExpense(),
                            onAddIncome: () => this.handleAddIncome(),
                            onPhotoScan: () => this.handlePhotoScan(),
                            onVoiceInput: () => this.handleVoiceInput()
                        };
                    };
                    componentCall.paramsGenerator_ = paramsLambda;
                }
                else {
                    this.updateStateVarsOfChildByElmtId(elmtId, {});
                }
            }, { name: "QuickActionBar" });
        }
        __Common__.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 最近记录标题
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/Index.ets(46:11)", "entry");
            // 最近记录标题
            Row.width('100%');
            // 最近记录标题
            Row.margin({ bottom: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('最近记录');
            Text.debugLine("entry/src/main/ets/pages/Index.ets(47:13)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/Index.ets(52:13)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('查看全部');
            Text.debugLine("entry/src/main/ets/pages/Index.ets(54:13)", "entry");
            Text.fontSize(14);
            Text.fontColor('#1976D2');
            Text.onClick(() => {
                // 跳转到账单列表页面
            });
        }, Text);
        Text.pop();
        // 最近记录标题
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 账单列表
            if (this.accounts.length > 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.buildAccountList.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.buildEmptyState.bind(this)();
                });
            }
        }, If);
        If.pop();
        Column.pop();
        // 内容区域
        Scroll.pop();
        Column.pop();
    }
    buildHeader(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/Index.ets(82:5)", "entry");
            Row.width('100%');
            Row.padding({ left: 16, right: 16, top: 12, bottom: 16 });
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/Index.ets(83:7)", "entry");
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('MonterAI');
            Text.debugLine("entry/src/main/ets/pages/Index.ets(84:9)", "entry");
            Text.fontSize(24);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('智能记账助手');
            Text.debugLine("entry/src/main/ets/pages/Index.ets(89:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#757575');
            Text.margin({ top: 2 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/Index.ets(96:7)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 设置按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/Index.ets(99:7)", "entry");
            // 设置按钮
            Row.width(40);
            // 设置按钮
            Row.height(40);
            // 设置按钮
            Row.borderRadius(20);
            // 设置按钮
            Row.backgroundColor('#FFFFFF');
            // 设置按钮
            Row.justifyContent(FlexAlign.Center);
            // 设置按钮
            Row.alignItems(VerticalAlign.Center);
            // 设置按钮
            Row.shadow({
                radius: 4,
                color: '#1F000000',
                offsetX: 0,
                offsetY: 2
            });
            // 设置按钮
            Row.onClick(() => {
                // 跳转到设置页面
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('⚙️');
            Text.debugLine("entry/src/main/ets/pages/Index.ets(100:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        // 设置按钮
        Row.pop();
        Row.pop();
    }
    buildAccountList(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/Index.ets(126:5)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = (_item, index: number) => {
                const account = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    __Common__.create();
                    __Common__.margin({ bottom: index < 4 ? 8 : 0 });
                }, __Common__);
                {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        if (isInitialRender) {
                            let componentCall = new AccountCard(this, {
                                account: account,
                                showDate: true,
                                onItemClick: (account: Account) => this.handleAccountClick(account),
                                onItemLongPress: (account: Account) => this.handleAccountLongPress(account)
                            }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/Index.ets", line: 128, col: 9 });
                            ViewPU.create(componentCall);
                            let paramsLambda = () => {
                                return {
                                    account: account,
                                    showDate: true,
                                    onItemClick: (account: Account) => this.handleAccountClick(account),
                                    onItemLongPress: (account: Account) => this.handleAccountLongPress(account)
                                };
                            };
                            componentCall.paramsGenerator_ = paramsLambda;
                        }
                        else {
                            this.updateStateVarsOfChildByElmtId(elmtId, {
                                account: account,
                                showDate: true
                            });
                        }
                    }, { name: "AccountCard" });
                }
                __Common__.pop();
            };
            this.forEachUpdateFunction(elmtId, this.accounts.slice(0, 5), forEachItemGenFunction, undefined, true, false);
        }, ForEach);
        ForEach.pop();
        Column.pop();
    }
    buildEmptyState(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/Index.ets(141:5)", "entry");
            Column.width('100%');
            Column.padding(40);
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📊');
            Text.debugLine("entry/src/main/ets/pages/Index.ets(142:7)", "entry");
            Text.fontSize(48);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('还没有记录');
            Text.debugLine("entry/src/main/ets/pages/Index.ets(146:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#757575');
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('点击下方按钮开始记账吧');
            Text.debugLine("entry/src/main/ets/pages/Index.ets(151:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#9E9E9E');
        }, Text);
        Text.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "Index";
    }
}
registerNamedRoute(() => new Index(undefined, {}), "", { bundleName: "com.silence.monter", moduleName: "entry", pagePath: "pages/Index", pageFullPath: "entry/src/main/ets/pages/Index", integratedHsp: "false", moduleType: "followWithHap" });
