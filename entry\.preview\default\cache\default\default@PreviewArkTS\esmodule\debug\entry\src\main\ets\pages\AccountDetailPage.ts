if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface AccountDetailPage_Params {
    account?: Account | null;
    isLoading?: boolean;
    isEditing?: boolean;
    showDeleteDialog?: boolean;
    accountId?: string;
    databaseService?: DatabaseService;
}
import { AccountType } from "@normalized:N&&&entry/src/main/ets/models/Account&";
import type { Account } from "@normalized:N&&&entry/src/main/ets/models/Account&";
import { DatabaseService } from "@normalized:N&&&entry/src/main/ets/services/DatabaseService&";
import { DateUtils } from "@normalized:N&&&entry/src/main/ets/common/utils/DateUtils&";
import router from "@ohos:router";
class AccountDetailPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__account = new ObservedPropertyObjectPU(null, this, "account");
        this.__isLoading = new ObservedPropertySimplePU(true, this, "isLoading");
        this.__isEditing = new ObservedPropertySimplePU(false, this, "isEditing");
        this.__showDeleteDialog = new ObservedPropertySimplePU(false, this, "showDeleteDialog");
        this.accountId = '';
        this.databaseService = DatabaseService.getInstance();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: AccountDetailPage_Params) {
        if (params.account !== undefined) {
            this.account = params.account;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.isEditing !== undefined) {
            this.isEditing = params.isEditing;
        }
        if (params.showDeleteDialog !== undefined) {
            this.showDeleteDialog = params.showDeleteDialog;
        }
        if (params.accountId !== undefined) {
            this.accountId = params.accountId;
        }
        if (params.databaseService !== undefined) {
            this.databaseService = params.databaseService;
        }
    }
    updateStateVars(params: AccountDetailPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__account.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__isEditing.purgeDependencyOnElmtId(rmElmtId);
        this.__showDeleteDialog.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__account.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__isEditing.aboutToBeDeleted();
        this.__showDeleteDialog.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __account: ObservedPropertyObjectPU<Account | null>;
    get account() {
        return this.__account.get();
    }
    set account(newValue: Account | null) {
        this.__account.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __isEditing: ObservedPropertySimplePU<boolean>;
    get isEditing() {
        return this.__isEditing.get();
    }
    set isEditing(newValue: boolean) {
        this.__isEditing.set(newValue);
    }
    private __showDeleteDialog: ObservedPropertySimplePU<boolean>;
    get showDeleteDialog() {
        return this.__showDeleteDialog.get();
    }
    set showDeleteDialog(newValue: boolean) {
        this.__showDeleteDialog.set(newValue);
    }
    private accountId: string;
    private databaseService: DatabaseService;
    aboutToAppear() {
        const params = router.getParams() as Record<string, Object>;
        if (params && params['accountId']) {
            this.accountId = params['accountId'] as string;
            this.loadAccount();
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(27:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        // 顶部导航栏
        this.buildHeader.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.buildLoadingView.bind(this)();
                });
            }
            else if (this.account) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.buildAccountDetail.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.buildErrorView.bind(this)();
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 删除确认对话框
            if (this.showDeleteDialog) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.buildDeleteDialog.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    buildHeader(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(51:5)", "entry");
            Row.width('100%');
            Row.padding({ left: 16, right: 16, top: 12, bottom: 12 });
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 返回按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(53:7)", "entry");
            // 返回按钮
            Row.width(40);
            // 返回按钮
            Row.height(40);
            // 返回按钮
            Row.borderRadius(20);
            // 返回按钮
            Row.justifyContent(FlexAlign.Center);
            // 返回按钮
            Row.alignItems(VerticalAlign.Center);
            // 返回按钮
            Row.onClick(() => {
                router.back();
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('←');
            Text.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(54:9)", "entry");
            Text.fontSize(20);
            Text.fontColor('#212121');
        }, Text);
        Text.pop();
        // 返回按钮
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('账单详情');
            Text.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(67:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 更多操作按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(75:7)", "entry");
            // 更多操作按钮
            Row.width(40);
            // 更多操作按钮
            Row.height(40);
            // 更多操作按钮
            Row.borderRadius(20);
            // 更多操作按钮
            Row.justifyContent(FlexAlign.Center);
            // 更多操作按钮
            Row.alignItems(VerticalAlign.Center);
            // 更多操作按钮
            Row.onClick(() => {
                this.showMoreActions();
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('⋯');
            Text.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(76:9)", "entry");
            Text.fontSize(20);
            Text.fontColor('#212121');
        }, Text);
        Text.pop();
        // 更多操作按钮
        Row.pop();
        Row.pop();
    }
    buildLoadingView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(96:5)", "entry");
            Column.width('100%');
            Column.layoutWeight(1);
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('⏳');
            Text.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(97:7)", "entry");
            Text.fontSize(48);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('加载中...');
            Text.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(101:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#757575');
        }, Text);
        Text.pop();
        Column.pop();
    }
    buildErrorView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(113:5)", "entry");
            Column.width('100%');
            Column.layoutWeight(1);
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('❌');
            Text.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(114:7)", "entry");
            Text.fontSize(48);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('账单不存在');
            Text.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(118:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#757575');
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('返回');
            Button.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(123:7)", "entry");
            Button.width(100);
            Button.height(40);
            Button.fontSize(14);
            Button.backgroundColor('#1976D2');
            Button.borderRadius(20);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        Button.pop();
        Column.pop();
    }
    buildAccountDetail(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(141:5)", "entry");
            Scroll.layoutWeight(1);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(142:7)", "entry");
            Column.padding(16);
        }, Column);
        // 金额卡片
        this.buildAmountCard.bind(this)();
        // 基本信息
        this.buildBasicInfo.bind(this)();
        // 扩展信息
        this.buildExtendedInfo.bind(this)();
        // 操作按钮
        this.buildActionButtons.bind(this)();
        Column.pop();
        Scroll.pop();
    }
    buildAmountCard(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(162:5)", "entry");
            Column.width('100%');
            Column.padding(24);
            Column.backgroundColor('#FFFFFF');
            Column.borderRadius(12);
            Column.alignItems(HorizontalAlign.Center);
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 金额
            Text.create(this.account!.getFormattedAmount());
            Text.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(164:7)", "entry");
            // 金额
            Text.fontSize(36);
            // 金额
            Text.fontWeight(FontWeight.Bold);
            // 金额
            Text.fontColor(this.account!.getAmountColor());
            // 金额
            Text.margin({ bottom: 8 });
        }, Text);
        // 金额
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 类型标签
            Text.create(this.account!.type === AccountType.INCOME ? '收入' : '支出');
            Text.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(171:7)", "entry");
            // 类型标签
            Text.fontSize(14);
            // 类型标签
            Text.fontColor('#FFFFFF');
            // 类型标签
            Text.backgroundColor(this.account!.getAmountColor());
            // 类型标签
            Text.padding({ left: 12, right: 12, top: 4, bottom: 4 });
            // 类型标签
            Text.borderRadius(12);
        }, Text);
        // 类型标签
        Text.pop();
        Column.pop();
    }
    buildBasicInfo(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(188:5)", "entry");
            Column.width('100%');
            Column.padding(20);
            Column.backgroundColor('#FFFFFF');
            Column.borderRadius(12);
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('基本信息');
            Text.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(189:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        // 分类
        this.buildInfoRow.bind(this)('分类', this.account!.category, '🏷️');
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 描述
            if (this.account!.description) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.buildInfoRow.bind(this)('描述', this.account!.description, '📝');
                });
            }
            // 日期
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        // 日期
        this.buildInfoRow.bind(this)('日期', DateUtils.formatDate(this.account!.date, 'YYYY-MM-DD HH:mm'), '📅');
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 位置
            if (this.account!.location) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.buildInfoRow.bind(this)('位置', this.account!.location, '📍');
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    buildExtendedInfo(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(221:5)", "entry");
            Column.width('100%');
            Column.padding(20);
            Column.backgroundColor('#FFFFFF');
            Column.borderRadius(12);
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('扩展信息');
            Text.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(222:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // AI生成标识
            if (this.account!.isAIGenerated) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(231:9)", "entry");
                        Row.width('100%');
                        Row.margin({ bottom: 12 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('🤖');
                        Text.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(232:11)", "entry");
                        Text.fontSize(16);
                        Text.margin({ right: 8 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('AI识别生成');
                        Text.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(236:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#212121');
                        Text.layoutWeight(1);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`置信度: ${(this.account!.aiConfidence! * 100).toFixed(0)}%`);
                        Text.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(241:11)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#757575');
                    }, Text);
                    Text.pop();
                    Row.pop();
                });
            }
            // 标签
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 标签
            if (this.account!.tags && this.account!.tags.length > 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(251:9)", "entry");
                        Column.width('100%');
                        Column.alignItems(HorizontalAlign.Start);
                        Column.margin({ bottom: 12 });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('标签');
                        Text.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(252:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#757575');
                        Text.alignSelf(ItemAlign.Start);
                        Text.margin({ bottom: 8 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Flex.create({ wrap: FlexWrap.Wrap });
                        Flex.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(258:11)", "entry");
                    }, Flex);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const tag = _item;
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create(tag);
                                Text.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(260:15)", "entry");
                                Text.fontSize(12);
                                Text.fontColor('#1976D2');
                                Text.backgroundColor('#E3F2FD');
                                Text.padding({ left: 8, right: 8, top: 4, bottom: 4 });
                                Text.borderRadius(12);
                                Text.margin({ right: 8, bottom: 8 });
                            }, Text);
                            Text.pop();
                        };
                        this.forEachUpdateFunction(elmtId, this.account!.tags, forEachItemGenFunction);
                    }, ForEach);
                    ForEach.pop();
                    Flex.pop();
                    Column.pop();
                });
            }
            // 创建时间
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 创建时间
            Text.create(`创建时间: ${DateUtils.getRelativeTime(this.account!.date)}`);
            Text.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(276:7)", "entry");
            // 创建时间
            Text.fontSize(12);
            // 创建时间
            Text.fontColor('#9E9E9E');
            // 创建时间
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        // 创建时间
        Text.pop();
        Column.pop();
    }
    buildInfoRow(label: string, value: string, icon: string, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(290:5)", "entry");
            Row.width('100%');
            Row.margin({ bottom: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(icon);
            Text.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(291:7)", "entry");
            Text.fontSize(16);
            Text.margin({ right: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(295:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(label);
            Text.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(296:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#757575');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(value);
            Text.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(301:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ top: 2 });
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
    }
    buildActionButtons(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(316:5)", "entry");
            Row.width('100%');
            Row.justifyContent(FlexAlign.SpaceBetween);
            Row.margin({ top: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('编辑');
            Button.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(317:7)", "entry");
            Button.width('45%');
            Button.height(48);
            Button.fontSize(16);
            Button.backgroundColor('#1976D2');
            Button.borderRadius(8);
            Button.onClick(() => {
                this.editAccount();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('删除');
            Button.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(327:7)", "entry");
            Button.width('45%');
            Button.height(48);
            Button.fontSize(16);
            Button.backgroundColor('#F44336');
            Button.borderRadius(8);
            Button.onClick(() => {
                this.showDeleteDialog = true;
            });
        }, Button);
        Button.pop();
        Row.pop();
    }
    buildDeleteDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(344:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#********');
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
            Column.onClick(() => {
                this.showDeleteDialog = false;
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(345:7)", "entry");
            Column.width('80%');
            Column.padding(24);
            Column.backgroundColor('#FFFFFF');
            Column.borderRadius(12);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('确认删除');
            Text.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(346:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('确定要删除这条账单记录吗？删除后无法恢复。');
            Text.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(352:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#757575');
            Text.textAlign(TextAlign.Center);
            Text.margin({ bottom: 24 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(358:9)", "entry");
            Row.width('100%');
            Row.justifyContent(FlexAlign.SpaceBetween);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(359:11)", "entry");
            Button.width('45%');
            Button.height(40);
            Button.fontSize(14);
            Button.backgroundColor('#E0E0E0');
            Button.fontColor('#212121');
            Button.borderRadius(8);
            Button.onClick(() => {
                this.showDeleteDialog = false;
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('删除');
            Button.debugLine("entry/src/main/ets/pages/AccountDetailPage.ets(370:11)", "entry");
            Button.width('45%');
            Button.height(40);
            Button.fontSize(14);
            Button.backgroundColor('#F44336');
            Button.borderRadius(8);
            Button.onClick(() => {
                this.deleteAccount();
            });
        }, Button);
        Button.pop();
        Row.pop();
        Column.pop();
        Column.pop();
    }
    // 私有方法
    private async loadAccount(): Promise<void> {
        this.isLoading = true;
        try {
            this.account = await this.databaseService.getAccountById(this.accountId);
        }
        catch (error) {
            console.error('加载账单详情失败:', error);
        }
        finally {
            this.isLoading = false;
        }
    }
    private editAccount(): void {
        if (this.account) {
            router.pushUrl({
                url: 'pages/AddAccountPage',
                params: {
                    account: this.account,
                    isEdit: true
                }
            });
        }
    }
    private async deleteAccount(): Promise<void> {
        if (this.account) {
            try {
                const success = await this.databaseService.deleteAccount(this.account.id);
                if (success) {
                    console.log('账单删除成功');
                    router.back();
                }
                else {
                    console.error('账单删除失败');
                }
            }
            catch (error) {
                console.error('删除账单时出错:', error);
            }
        }
        this.showDeleteDialog = false;
    }
    private showMoreActions(): void {
        console.log('显示更多操作');
        // TODO: 显示更多操作菜单（分享、导出等）
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "AccountDetailPage";
    }
}
registerNamedRoute(() => new AccountDetailPage(undefined, {}), "", { bundleName: "com.silence.monter", moduleName: "entry", pagePath: "pages/AccountDetailPage", pageFullPath: "entry/src/main/ets/pages/AccountDetailPage", integratedHsp: "false", moduleType: "followWithHap" });
