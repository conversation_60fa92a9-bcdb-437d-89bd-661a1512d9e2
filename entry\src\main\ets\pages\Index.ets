import { Account, AccountType, AccountSummary } from '../models/Account';
import { AccountCard } from '../components/AccountCard';
import { SummaryCard } from '../components/SummaryCard';
import { QuickActionBar } from '../components/QuickActionBar';
import { DateUtils } from '../common/utils/DateUtils';
import router from '@ohos.router';

@Entry
@Component
struct Index {
  @State accounts: Account[] = [];
  @State summary: AccountSummary = new AccountSummary([]);
  @State selectedPeriod: string = '本月';
  @State isLoading: boolean = false;

  aboutToAppear() {
    this.loadMockData();
    this.updateSummary();
  }

  build() {
    Column() {
      // 顶部标题栏
      this.buildHeader()

      // 内容区域
      Scroll() {
        Column() {
          // 统计卡片
          SummaryCard({
            summary: this.summary,
            period: this.selectedPeriod
          })
          .margin({ bottom: 16 })

          // 快速操作栏
          QuickActionBar({
            onAddExpense: () => this.handleAddExpense(),
            onAddIncome: () => this.handleAddIncome(),
            onPhotoScan: () => this.handlePhotoScan(),
            onVoiceInput: () => this.handleVoiceInput()
          })
          .margin({ bottom: 16 })

          // 最近记录标题
          Row() {
            Text('最近记录')
              .fontSize(18)
              .fontWeight(FontWeight.Bold)
              .fontColor('#212121')

            Blank()

            Text('查看全部')
              .fontSize(14)
              .fontColor('#1976D2')
              .onClick(() => {
                // 跳转到账单列表页面
              })
          }
          .width('100%')
          .margin({ bottom: 12 })

          // 账单列表
          if (this.accounts.length > 0) {
            this.buildAccountList()
          } else {
            this.buildEmptyState()
          }
        }
        .padding({ left: 16, right: 16, bottom: 20 })
      }
      .layoutWeight(1)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder
  buildHeader() {
    Row() {
      Column() {
        Text('MonterAI')
          .fontSize(24)
          .fontWeight(FontWeight.Bold)
          .fontColor('#212121')

        Text('智能记账助手')
          .fontSize(14)
          .fontColor('#757575')
          .margin({ top: 2 })
      }
      .alignItems(HorizontalAlign.Start)

      Blank()

      // 设置按钮
      Row() {
        Text('⚙️')
          .fontSize(20)
      }
      .width(40)
      .height(40)
      .borderRadius(20)
      .backgroundColor('#FFFFFF')
      .justifyContent(FlexAlign.Center)
      .alignItems(VerticalAlign.Center)
      .shadow({
        radius: 4,
        color: '#1F000000',
        offsetX: 0,
        offsetY: 2
      })
      .onClick(() => {
        router.pushUrl({
          url: 'pages/AnalysisPage'
        });
      })
    }
    .width('100%')
    .padding({ left: 16, right: 16, top: 12, bottom: 16 })
    .backgroundColor('#FFFFFF')
  }

  @Builder
  buildAccountList() {
    Column() {
      ForEach(this.accounts.slice(0, 5), (account: Account, index: number) => {
        AccountCard({
          account: account,
          showDate: true,
          onItemClick: (account: Account) => this.handleAccountClick(account),
          onItemLongPress: (account: Account) => this.handleAccountLongPress(account)
        })
        .margin({ bottom: index < 4 ? 8 : 0 })
      })
    }
  }

  @Builder
  buildEmptyState() {
    Column() {
      Text('📊')
        .fontSize(48)
        .margin({ bottom: 16 })

      Text('还没有记录')
        .fontSize(16)
        .fontColor('#757575')
        .margin({ bottom: 8 })

      Text('点击下方按钮开始记账吧')
        .fontSize(14)
        .fontColor('#9E9E9E')
    }
    .width('100%')
    .padding(40)
    .alignItems(HorizontalAlign.Center)
  }

  // 事件处理方法
  private handleAddExpense() {
    console.log('添加支出');
    router.pushUrl({
      url: 'pages/AddAccountPage',
      params: {
        type: AccountType.EXPENSE
      }
    });
  }

  private handleAddIncome() {
    console.log('添加收入');
    router.pushUrl({
      url: 'pages/AddAccountPage',
      params: {
        type: AccountType.INCOME
      }
    });
  }

  private handlePhotoScan() {
    console.log('拍照识别');
    router.pushUrl({
      url: 'pages/PhotoScanPage'
    });
  }

  private handleVoiceInput() {
    console.log('语音记账');
    router.pushUrl({
      url: 'pages/VoiceInputPage'
    });
  }

  private handleAccountClick(account: Account) {
    console.log('点击账单:', account.id);
    // TODO: 跳转到账单详情页面
  }

  private handleAccountLongPress(account: Account) {
    console.log('长按账单:', account.id);
    // TODO: 显示账单操作菜单
  }

  // 数据处理方法
  private loadMockData() {
    // 模拟数据
    this.accounts = [
      new Account({
        amount: 25.50,
        type: AccountType.EXPENSE,
        category: '餐饮',
        description: '午餐',
        date: new Date(),
        location: '公司附近'
      }),
      new Account({
        amount: 8000.00,
        type: AccountType.INCOME,
        category: '工资',
        description: '月薪',
        date: new Date(Date.now() - 24 * 60 * 60 * 1000),
        isAIGenerated: false
      }),
      new Account({
        amount: 120.00,
        type: AccountType.EXPENSE,
        category: '交通',
        description: '地铁卡充值',
        date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
      }),
      new Account({
        amount: 299.00,
        type: AccountType.EXPENSE,
        category: '购物',
        description: '运动鞋',
        date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
        isAIGenerated: true,
        aiConfidence: 0.85
      }),
      new Account({
        amount: 50.00,
        type: AccountType.EXPENSE,
        category: '娱乐',
        description: '电影票',
        date: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000)
      })
    ];
  }

  private updateSummary() {
    this.summary = new AccountSummary(this.accounts);
  }
}