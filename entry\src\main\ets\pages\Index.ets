import { Account, AccountType, AccountSummary } from '../models/Account';
import { AccountCard } from '../components/AccountCard';
import { SummaryCard } from '../components/SummaryCard';
import { QuickActionBar } from '../components/QuickActionBar';
import { DateUtils } from '../common/utils/DateUtils';
import router from '@ohos.router';

@Entry
@Component
struct Index {
  @State accounts: Account[] = [];
  @State summary: AccountSummary = new AccountSummary([]);
  @State selectedPeriod: string = '本月';
  @State isLoading: boolean = false;

  aboutToAppear() {
    this.loadMockData();
    this.updateSummary();
  }

  build() {
    Column() {
      // 顶部标题栏
      this.buildHeader()

      // 内容区域
      Scroll() {
        Column() {
          // 统计卡片
          SummaryCard({
            summary: this.summary,
            period: this.selectedPeriod
          })
          .margin({ bottom: 16 })

          // 快速操作栏
          QuickActionBar({
            onAddExpense: (): void => this.handleAddExpense(),
            onAddIncome: (): void => this.handleAddIncome(),
            onPhotoScan: (): void => this.handlePhotoScan(),
            onVoiceInput: (): void => this.handleVoiceInput()
          })
          .margin({ bottom: 16 })

          // 最近记录标题
          Row() {
            Text('最近记录')
              .fontSize(18)
              .fontWeight(FontWeight.Bold)
              .fontColor('#212121')

            Blank()

            Text('查看全部')
              .fontSize(14)
              .fontColor('#1976D2')
              .onClick(() => {
                // 跳转到账单列表页面
              })
          }
          .width('100%')
          .margin({ bottom: 12 })

          // 账单列表
          if (this.accounts.length > 0) {
            this.buildAccountList()
          } else {
            this.buildEmptyState()
          }
        }
        .padding({ left: 16, right: 16, bottom: 20 })
      }
      .layoutWeight(1)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder
  buildHeader() {
    Row() {
      Column() {
        Text('MonterAI')
          .fontSize(24)
          .fontWeight(FontWeight.Bold)
          .fontColor('#212121')

        Text('智能记账助手')
          .fontSize(14)
          .fontColor('#757575')
          .margin({ top: 2 })
      }
      .alignItems(HorizontalAlign.Start)

      Blank()

      // 设置按钮
      Row() {
        Text('⚙️')
          .fontSize(20)
      }
      .width(40)
      .height(40)
      .borderRadius(20)
      .backgroundColor('#FFFFFF')
      .justifyContent(FlexAlign.Center)
      .alignItems(VerticalAlign.Center)
      .shadow({
        radius: 4,
        color: '#1F000000',
        offsetX: 0,
        offsetY: 2
      })
      .onClick(() => {
        router.pushUrl({
          url: 'pages/SettingsPage'
        });
      })
    }
    .width('100%')
    .padding({ left: 16, right: 16, top: 12, bottom: 16 })
    .backgroundColor('#FFFFFF')
  }

  @Builder
  buildAccountList() {
    Column() {
      ForEach(this.accounts.slice(0, 5), (account: Account, index: number) => {
        AccountCard({
          account: account,
          showDate: true,
          onItemClick: (account: Account): void => this.handleAccountClick(account),
          onItemLongPress: (account: Account): void => this.handleAccountLongPress(account)
        })
        .margin({ bottom: index < 4 ? 8 : 0 })
      })
    }
  }

  @Builder
  buildEmptyState() {
    Column() {
      Text('📊')
        .fontSize(48)
        .margin({ bottom: 16 })

      Text('还没有记录')
        .fontSize(16)
        .fontColor('#757575')
        .margin({ bottom: 8 })

      Text('点击下方按钮开始记账吧')
        .fontSize(14)
        .fontColor('#9E9E9E')
    }
    .width('100%')
    .padding(40)
    .alignItems(HorizontalAlign.Center)
  }

  // 事件处理方法
  private handleAddExpense() {
    console.log('添加支出');
    router.pushUrl({
      url: 'pages/AddAccountPage',
      params: {
        type: AccountType.EXPENSE
      }
    });
  }

  private handleAddIncome() {
    console.log('添加收入');
    router.pushUrl({
      url: 'pages/AddAccountPage',
      params: {
        type: AccountType.INCOME
      }
    });
  }

  private handlePhotoScan() {
    console.log('拍照识别');
    router.pushUrl({
      url: 'pages/PhotoScanPage'
    });
  }

  private handleVoiceInput() {
    console.log('语音记账');
    router.pushUrl({
      url: 'pages/VoiceInputPage'
    });
  }

  private handleAccountClick(account: Account) {
    console.log('点击账单:', account.id);
    // TODO: 跳转到账单详情页面
  }

  private handleAccountLongPress(account: Account) {
    console.log('长按账单:', account.id);
    // TODO: 显示账单操作菜单
  }

  // 数据处理方法
  private loadMockData() {
    // 模拟数据
    this.accounts = [];

    const account1 = new Account();
    account1.amount = 25.50;
    account1.type = AccountType.EXPENSE;
    account1.category = '餐饮';
    account1.description = '午餐';
    account1.date = new Date();
    account1.location = '公司附近';
    this.accounts.push(account1);

    const account2 = new Account();
    account2.amount = 8000.00;
    account2.type = AccountType.INCOME;
    account2.category = '工资';
    account2.description = '月薪';
    account2.date = new Date(Date.now() - 24 * 60 * 60 * 1000);
    account2.isAIGenerated = false;
    this.accounts.push(account2);

    const account3 = new Account();
    account3.amount = 120.00;
    account3.type = AccountType.EXPENSE;
    account3.category = '交通';
    account3.description = '地铁卡充值';
    account3.date = new Date(Date.now() - 2 * 24 * 60 * 60 * 1000);
    this.accounts.push(account3);
  }

  private updateSummary() {
    this.summary = new AccountSummary(this.accounts);
  }
}