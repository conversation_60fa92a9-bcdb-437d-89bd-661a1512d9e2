import { SyncService, DeviceInfo, SyncStatus } from '../services/SyncService';
import { DateUtils } from '../common/utils/DateUtils';
import router from '@ohos.router';

@Entry
@Component
struct DeviceManagePage {
  @State devices: DeviceInfo[] = [];
  @State syncStatus: SyncStatus | null = null;
  @State isLoading: boolean = false;
  @State isSyncing: boolean = false;

  private syncService: SyncService = SyncService.getInstance();

  aboutToAppear() {
    this.loadDevices();
    this.loadSyncStatus();
  }

  build() {
    Column() {
      // 顶部导航栏
      this.buildHeader()

      // 内容区域
      Scroll() {
        Column() {
          // 同步状态卡片
          this.buildSyncStatusCard()

          // 设备列表
          this.buildDeviceList()

          // 同步设置
          this.buildSyncSettings()
        }
        .padding(16)
      }
      .layoutWeight(1)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder
  buildHeader() {
    Row() {
      // 返回按钮
      Row() {
        Text('←')
          .fontSize(20)
          .fontColor('#212121')
      }
      .width(40)
      .height(40)
      .borderRadius(20)
      .justifyContent(FlexAlign.Center)
      .alignItems(VerticalAlign.Center)
      .onClick(() => {
        router.back();
      })

      Text('设备管理')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .fontColor('#212121')
        .layoutWeight(1)
        .textAlign(TextAlign.Center)

      // 刷新按钮
      Row() {
        Text('🔄')
          .fontSize(16)
          .fontColor('#212121')
      }
      .width(40)
      .height(40)
      .borderRadius(20)
      .justifyContent(FlexAlign.Center)
      .alignItems(VerticalAlign.Center)
      .onClick(() => {
        this.refreshData();
      })
    }
    .width('100%')
    .padding({ left: 16, right: 16, top: 12, bottom: 12 })
    .backgroundColor('#FFFFFF')
  }

  @Builder
  buildSyncStatusCard() {
    Column() {
      Row() {
        Text('📡')
          .fontSize(20)
          .margin({ right: 8 })

        Text('同步状态')
          .fontSize(16)
          .fontWeight(FontWeight.Bold)
          .fontColor('#212121')

        Blank()

        if (this.syncStatus?.isEnabled) {
          Text('已启用')
            .fontSize(12)
            .fontColor('#FFFFFF')
            .backgroundColor('#4CAF50')
            .padding({ left: 8, right: 8, top: 2, bottom: 2 })
            .borderRadius(10)
        } else {
          Text('已禁用')
            .fontSize(12)
            .fontColor('#FFFFFF')
            .backgroundColor('#F44336')
            .padding({ left: 8, right: 8, top: 2, bottom: 2 })
            .borderRadius(10)
        }
      }
      .width('100%')
      .margin({ bottom: 16 })

      if (this.syncStatus) {
        Column() {
          Row() {
            Text('连接设备:')
              .fontSize(14)
              .fontColor('#757575')
              .width(80)

            Text(`${this.syncStatus.connectedDevices}/${this.syncStatus.totalDevices}`)
              .fontSize(14)
              .fontColor('#212121')
          }
          .width('100%')
          .margin({ bottom: 8 })

          Row() {
            Text('最后同步:')
              .fontSize(14)
              .fontColor('#757575')
              .width(80)

            Text(this.syncStatus.lastSyncTime ? 
                 DateUtils.getRelativeTime(this.syncStatus.lastSyncTime) : '从未同步')
              .fontSize(14)
              .fontColor('#212121')
          }
          .width('100%')
          .margin({ bottom: 16 })

          Button(this.isSyncing ? '同步中...' : '立即同步')
            .width('100%')
            .height(40)
            .fontSize(14)
            .backgroundColor(this.isSyncing ? '#CCCCCC' : '#1976D2')
            .borderRadius(8)
            .enabled(!this.isSyncing)
            .onClick(() => {
              this.triggerSync();
            })
        }
      }
    }
    .width('100%')
    .padding(20)
    .backgroundColor('#FFFFFF')
    .borderRadius(12)
    .margin({ bottom: 16 })
  }

  @Builder
  buildDeviceList() {
    Column() {
      Text('设备列表')
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .fontColor('#212121')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 16 })

      if (this.devices.length > 0) {
        ForEach(this.devices, (device: DeviceInfo, index: number) => {
          Row() {
            // 设备图标
            Text(this.getDeviceIcon(device.deviceType))
              .fontSize(24)
              .margin({ right: 12 })

            // 设备信息
            Column() {
              Text(device.deviceName)
                .fontSize(16)
                .fontWeight(FontWeight.Medium)
                .fontColor('#212121')
                .alignSelf(ItemAlign.Start)

              Text(`最后同步: ${DateUtils.getRelativeTime(device.lastSyncTime)}`)
                .fontSize(12)
                .fontColor('#757575')
                .alignSelf(ItemAlign.Start)
                .margin({ top: 2 })
            }
            .layoutWeight(1)
            .alignItems(HorizontalAlign.Start)

            // 在线状态
            Row() {
              Text(device.isOnline ? '●' : '●')
                .fontSize(12)
                .fontColor(device.isOnline ? '#4CAF50' : '#9E9E9E')
                .margin({ right: 4 })

              Text(device.isOnline ? '在线' : '离线')
                .fontSize(12)
                .fontColor(device.isOnline ? '#4CAF50' : '#9E9E9E')
            }
          }
          .width('100%')
          .padding(16)
          .backgroundColor('#FFFFFF')
          .borderRadius(8)
          .margin({ bottom: index < this.devices.length - 1 ? 8 : 0 })
          .onClick(() => {
            this.showDeviceDetails(device);
          })
        })
      } else {
        Column() {
          Text('📱')
            .fontSize(48)
            .margin({ bottom: 12 })

          Text('暂无设备')
            .fontSize(16)
            .fontColor('#757575')
            .margin({ bottom: 8 })

          Text('请确保其他设备已登录同一账号')
            .fontSize(14)
            .fontColor('#9E9E9E')
        }
        .width('100%')
        .padding(40)
        .backgroundColor('#FFFFFF')
        .borderRadius(8)
        .alignItems(HorizontalAlign.Center)
      }
    }
    .width('100%')
    .margin({ bottom: 16 })
  }

  @Builder
  buildSyncSettings() {
    Column() {
      Text('同步设置')
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .fontColor('#212121')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 16 })

      // 自动同步开关
      Row() {
        Column() {
          Text('自动同步')
            .fontSize(14)
            .fontColor('#212121')
            .alignSelf(ItemAlign.Start)

          Text('在设备间自动同步数据')
            .fontSize(12)
            .fontColor('#757575')
            .alignSelf(ItemAlign.Start)
            .margin({ top: 2 })
        }
        .layoutWeight(1)
        .alignItems(HorizontalAlign.Start)

        Toggle({ type: ToggleType.Switch, isOn: true })
          .onChange((isOn: boolean) => {
            this.syncService.setAutoSync(isOn);
          })
      }
      .width('100%')
      .padding(16)
      .backgroundColor('#FFFFFF')
      .borderRadius(8)
      .margin({ bottom: 8 })

      // 仅WiFi同步
      Row() {
        Column() {
          Text('仅WiFi同步')
            .fontSize(14)
            .fontColor('#212121')
            .alignSelf(ItemAlign.Start)

          Text('仅在WiFi环境下同步数据')
            .fontSize(12)
            .fontColor('#757575')
            .alignSelf(ItemAlign.Start)
            .margin({ top: 2 })
        }
        .layoutWeight(1)
        .alignItems(HorizontalAlign.Start)

        Toggle({ type: ToggleType.Switch, isOn: true })
          .onChange((isOn: boolean) => {
            console.log('仅WiFi同步:', isOn);
          })
      }
      .width('100%')
      .padding(16)
      .backgroundColor('#FFFFFF')
      .borderRadius(8)
    }
    .width('100%')
  }

  // 私有方法
  private async loadDevices() {
    this.isLoading = true;
    try {
      this.devices = await this.syncService.getAvailableDevices();
    } catch (error) {
      console.error('加载设备列表失败:', error);
    } finally {
      this.isLoading = false;
    }
  }

  private async loadSyncStatus() {
    try {
      this.syncStatus = await this.syncService.getSyncStatus();
    } catch (error) {
      console.error('加载同步状态失败:', error);
    }
  }

  private async refreshData() {
    await Promise.all([
      this.loadDevices(),
      this.loadSyncStatus()
    ]);
  }

  private async triggerSync() {
    this.isSyncing = true;
    try {
      const success = await this.syncService.triggerSync();
      if (success) {
        console.log('同步成功');
        await this.loadSyncStatus();
      } else {
        console.log('同步失败');
      }
    } catch (error) {
      console.error('同步失败:', error);
    } finally {
      this.isSyncing = false;
    }
  }

  private showDeviceDetails(device: DeviceInfo) {
    console.log('显示设备详情:', device);
    // TODO: 显示设备详情对话框
  }

  private getDeviceIcon(deviceType: string): string {
    const iconMap: Record<string, string> = {
      'phone': '📱',
      'tablet': '📱',
      'wearable': '⌚',
      'tv': '📺',
      'car': '🚗'
    };
    return iconMap[deviceType] || '📱';
  }
}
