import { Account, AccountType } from '../models/Account';
import { AIService, VoiceParseResult } from '../services/AIService';
import router from '@ohos.router';

@Entry
@Component
struct VoiceInputPage {
  @State isRecording: boolean = false;
  @State isProcessing: boolean = false;
  @State recordingTime: number = 0;
  @State voiceText: string = '';
  @State parseResult: VoiceParseResult | null = null;
  @State showResult: boolean = false;
  @State errorMessage: string = '';

  private aiService: AIService = AIService.getInstance();
  private recordingTimer: number = -1;

  aboutToDisappear() {
    if (this.recordingTimer !== -1) {
      clearInterval(this.recordingTimer);
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      this.buildHeader()

      if (!this.showResult) {
        // 语音录制界面
        this.buildRecordingView()
      } else {
        // 识别结果界面
        this.buildResultView()
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder
  buildHeader() {
    Row() {
      // 返回按钮
      Row() {
        Text('←')
          .fontSize(20)
          .fontColor('#212121')
      }
      .width(40)
      .height(40)
      .borderRadius(20)
      .justifyContent(FlexAlign.Center)
      .alignItems(VerticalAlign.Center)
      .onClick(() => {
        router.back();
      })

      Text('语音记账')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .fontColor('#212121')
        .layoutWeight(1)
        .textAlign(TextAlign.Center)

      // 帮助按钮
      Row() {
        Text('?')
          .fontSize(16)
          .fontColor('#212121')
      }
      .width(40)
      .height(40)
      .borderRadius(20)
      .justifyContent(FlexAlign.Center)
      .alignItems(VerticalAlign.Center)
      .onClick(() => {
        this.showHelpDialog();
      })
    }
    .width('100%')
    .padding({ left: 16, right: 16, top: 12, bottom: 12 })
    .backgroundColor('#FFFFFF')
  }

  @Builder
  buildRecordingView() {
    Column() {
      // 提示信息
      Column() {
        Text('🎤')
          .fontSize(80)
          .margin({ bottom: 20 })

        if (this.isProcessing) {
          Text('正在识别中...')
            .fontSize(18)
            .fontColor('#1976D2')
            .margin({ bottom: 8 })
        } else if (this.isRecording) {
          Text('正在录音...')
            .fontSize(18)
            .fontColor('#F44336')
            .margin({ bottom: 8 })
          
          Text(this.formatTime(this.recordingTime))
            .fontSize(24)
            .fontWeight(FontWeight.Bold)
            .fontColor('#F44336')
        } else {
          Text('点击按钮开始录音')
            .fontSize(18)
            .fontColor('#757575')
            .margin({ bottom: 8 })
        }

        // 语音示例
        if (!this.isRecording && !this.isProcessing) {
          Column() {
            Text('语音示例：')
              .fontSize(14)
              .fontColor('#9E9E9E')
              .margin({ bottom: 8 })

            Text('"今天午餐花了25块5"')
              .fontSize(14)
              .fontColor('#9E9E9E')
              .margin({ bottom: 4 })

            Text('"买菜花了50元"')
              .fontSize(14)
              .fontColor('#9E9E9E')
              .margin({ bottom: 4 })

            Text('"收到工资8000元"')
              .fontSize(14)
              .fontColor('#9E9E9E')
          }
          .margin({ top: 20 })
        }
      }
      .layoutWeight(1)
      .justifyContent(FlexAlign.Center)
      .alignItems(HorizontalAlign.Center)

      // 录音按钮
      Column() {
        Row() {
          if (this.isRecording) {
            Text('⏹️')
              .fontSize(40)
              .fontColor('#FFFFFF')
          } else {
            Text('🎤')
              .fontSize(40)
              .fontColor('#FFFFFF')
          }
        }
        .width(120)
        .height(120)
        .borderRadius(60)
        .backgroundColor(this.getRecordButtonColor())
        .justifyContent(FlexAlign.Center)
        .alignItems(VerticalAlign.Center)
        .enabled(!this.isProcessing)
        .onClick(() => {
          if (this.isRecording) {
            this.stopRecording();
          } else {
            this.startRecording();
          }
        })

        Text(this.getRecordButtonText())
          .fontSize(16)
          .fontColor('#757575')
          .margin({ top: 12 })
      }
      .margin({ bottom: 40 })

      // 错误信息
      if (this.errorMessage) {
        Text(this.errorMessage)
          .fontSize(14)
          .fontColor('#F44336')
          .textAlign(TextAlign.Center)
          .margin({ bottom: 20 })
      }
    }
    .width('100%')
    .padding(20)
  }

  @Builder
  buildResultView() {
    Column() {
      // 识别结果
      Scroll() {
        Column() {
          if (this.parseResult) {
            // 识别的文字
            Text('识别结果')
              .fontSize(16)
              .fontWeight(FontWeight.Bold)
              .fontColor('#212121')
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 8 })

            Text(`"${this.parseResult.text}"`)
              .fontSize(18)
              .fontColor('#1976D2')
              .backgroundColor('#E3F2FD')
              .padding(16)
              .borderRadius(8)
              .margin({ bottom: 20 })

            // 解析结果
            Text('解析信息')
              .fontSize(16)
              .fontWeight(FontWeight.Bold)
              .fontColor('#212121')
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 12 })

            Column() {
              Row() {
                Text('类型:')
                  .fontSize(14)
                  .fontColor('#757575')
                  .width(60)

                Text(this.parseResult.type === AccountType.INCOME ? '收入' : '支出')
                  .fontSize(14)
                  .fontColor(this.parseResult.type === AccountType.INCOME ? '#4CAF50' : '#F44336')
                  .fontWeight(FontWeight.Medium)
              }
              .width('100%')
              .margin({ bottom: 8 })

              Row() {
                Text('金额:')
                  .fontSize(14)
                  .fontColor('#757575')
                  .width(60)

                Text(`¥${this.parseResult.amount.toFixed(2)}`)
                  .fontSize(14)
                  .fontColor('#212121')
                  .fontWeight(FontWeight.Medium)
              }
              .width('100%')
              .margin({ bottom: 8 })

              Row() {
                Text('分类:')
                  .fontSize(14)
                  .fontColor('#757575')
                  .width(60)

                Text(this.parseResult.category)
                  .fontSize(14)
                  .fontColor('#212121')
                  .fontWeight(FontWeight.Medium)
              }
              .width('100%')
              .margin({ bottom: 8 })

              if (this.parseResult.description) {
                Row() {
                  Text('备注:')
                    .fontSize(14)
                    .fontColor('#757575')
                    .width(60)

                  Text(this.parseResult.description)
                    .fontSize(14)
                    .fontColor('#212121')
                    .fontWeight(FontWeight.Medium)
                }
                .width('100%')
                .margin({ bottom: 8 })
              }

              Row() {
                Text('准确度:')
                  .fontSize(14)
                  .fontColor('#757575')
                  .width(60)

                Text(`${(this.parseResult.confidence * 100).toFixed(0)}%`)
                  .fontSize(14)
                  .fontColor('#4CAF50')
                  .fontWeight(FontWeight.Medium)
              }
              .width('100%')
            }
            .width('100%')
            .backgroundColor('#FFFFFF')
            .padding(16)
            .borderRadius(8)
          }
        }
        .width('100%')
        .padding(20)
      }
      .layoutWeight(1)

      // 底部按钮
      Row() {
        Button('重新录音')
          .width('45%')
          .height(48)
          .fontSize(16)
          .backgroundColor('#9E9E9E')
          .borderRadius(8)
          .onClick(() => {
            this.resetRecording();
          })

        Button('确认记账')
          .width('45%')
          .height(48)
          .fontSize(16)
          .backgroundColor('#1976D2')
          .borderRadius(8)
          .enabled(this.parseResult !== null)
          .onClick(() => {
            this.confirmAndSave();
          })
      }
      .width('100%')
      .padding(20)
      .justifyContent(FlexAlign.SpaceBetween)
    }
  }

  // 事件处理方法
  private startRecording() {
    this.isRecording = true;
    this.recordingTime = 0;
    this.errorMessage = '';

    // 开始计时
    this.recordingTimer = setInterval(() => {
      this.recordingTime++;
      if (this.recordingTime >= 60) { // 最长60秒
        this.stopRecording();
      }
    }, 1000);

    console.log('开始录音');
    // TODO: 启动语音录制
  }

  private async stopRecording() {
    this.isRecording = false;
    this.isProcessing = true;

    if (this.recordingTimer !== -1) {
      clearInterval(this.recordingTimer);
      this.recordingTimer = -1;
    }

    try {
      console.log('停止录音，开始处理');
      
      // 模拟语音文件路径
      const audioPath = 'mock_audio_path.wav';
      
      // 调用AI识别
      this.parseResult = await this.aiService.parseVoiceInput(audioPath);
      this.showResult = true;

    } catch (error) {
      this.errorMessage = error.message || '识别失败，请重试';
    } finally {
      this.isProcessing = false;
    }
  }

  private resetRecording() {
    this.showResult = false;
    this.parseResult = null;
    this.errorMessage = '';
    this.recordingTime = 0;
    this.voiceText = '';
  }

  private confirmAndSave() {
    if (!this.parseResult) return;

    // 创建账单对象
    const account = new Account({
      amount: this.parseResult.amount,
      type: this.parseResult.type,
      category: this.parseResult.category,
      description: this.parseResult.description,
      date: new Date(),
      isAIGenerated: true,
      aiConfidence: this.parseResult.confidence
    });

    console.log('保存语音识别账单:', account);
    
    // TODO: 保存到数据库
    
    // 返回主页
    router.back();
  }

  private showHelpDialog() {
    console.log('显示语音记账帮助');
    // TODO: 显示语音记账帮助信息
  }

  // 辅助方法
  private getRecordButtonColor(): string {
    if (this.isProcessing) return '#9E9E9E';
    if (this.isRecording) return '#F44336';
    return '#1976D2';
  }

  private getRecordButtonText(): string {
    if (this.isProcessing) return '处理中...';
    if (this.isRecording) return '点击停止';
    return '点击录音';
  }

  private formatTime(seconds: number): string {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
}
