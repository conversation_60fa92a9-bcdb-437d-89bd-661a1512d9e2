/**
 * 用户数据模型
 */
export class User {
    id: string = '';
    username: string = '';
    avatar?: string;
    email?: string;
    phone?: string;
    settings: UserSettings = new UserSettings();
    createdAt: Date = new Date();
    lastLoginAt?: Date;
    constructor(data?: Partial<User>) {
        if (data) {
            Object.assign(this, data);
        }
        if (!this.id) {
            this.id = this.generateId();
        }
    }
    private generateId(): string {
        return Date.now().toString() + Math.random().toString(36).substring(2, 11);
    }
}
/**
 * 用户设置
 */
export class UserSettings {
    // 通用设置
    currency: string = 'CNY';
    language: string = 'zh-CN';
    theme: ThemeMode = ThemeMode.AUTO;
    // 记账设置
    defaultExpenseCategory: string = '';
    defaultIncomeCategory: string = '';
    enableVoiceInput: boolean = true;
    enablePhotoRecognition: boolean = true;
    // 预算设置
    monthlyBudget: number = 0;
    budgetWarningThreshold: number = 0.8; // 80%时提醒
    enableBudgetNotification: boolean = true;
    // AI设置
    enableAIAnalysis: boolean = true;
    enableAIRecommendation: boolean = true;
    aiConfidenceThreshold: number = 0.7; // AI识别置信度阈值
    // 同步设置
    enableCloudSync: boolean = false;
    enableDistributedSync: boolean = true;
    autoSyncInterval: number = 300; // 5分钟
    // 隐私设置
    enableBiometricAuth: boolean = false;
    enableDataEncryption: boolean = true;
    dataRetentionDays: number = 365; // 数据保留天数
    constructor(data?: Partial<UserSettings>) {
        if (data) {
            Object.assign(this, data);
        }
    }
}
/**
 * 主题模式枚举
 */
export enum ThemeMode {
    LIGHT = "light",
    DARK = "dark",
    AUTO = "auto"
}
/**
 * 预算配置
 */
export class Budget {
    id: string = '';
    name: string = '';
    amount: number = 0;
    period: BudgetPeriod = BudgetPeriod.MONTHLY;
    categories: string[] = []; // 关联的分类ID
    startDate: Date = new Date();
    endDate?: Date;
    isActive: boolean = true;
    constructor(data?: Partial<Budget>) {
        if (data) {
            Object.assign(this, data);
        }
        if (!this.id) {
            this.id = this.generateId();
        }
    }
    private generateId(): string {
        return Date.now().toString() + Math.random().toString(36).substring(2, 11);
    }
    /**
     * 获取预算进度百分比
     */
    getProgress(usedAmount: number): number {
        return Math.min((usedAmount / this.amount) * 100, 100);
    }
    /**
     * 检查是否超出预算
     */
    isOverBudget(usedAmount: number): boolean {
        return usedAmount > this.amount;
    }
}
/**
 * 预算周期枚举
 */
export enum BudgetPeriod {
    DAILY = "daily",
    WEEKLY = "weekly",
    MONTHLY = "monthly",
    YEARLY = "yearly"
}
