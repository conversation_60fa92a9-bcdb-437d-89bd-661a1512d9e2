/**
 * 日期工具类
 */
export class DateUtils {
    /**
     * 格式化日期
     */
    static formatDate(date: Date, format: string = 'YYYY-MM-DD'): string {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        return format
            .replace('YYYY', year.toString())
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    }
    /**
     * 获取今天的开始时间
     */
    static getTodayStart(): Date {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return today;
    }
    /**
     * 获取今天的结束时间
     */
    static getTodayEnd(): Date {
        const today = new Date();
        today.setHours(23, 59, 59, 999);
        return today;
    }
    /**
     * 获取本周的开始时间（周一）
     */
    static getWeekStart(): Date {
        const today = new Date();
        const dayOfWeek = today.getDay();
        const diff = dayOfWeek === 0 ? -6 : 1 - dayOfWeek; // 周日为0，调整为周一开始
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() + diff);
        weekStart.setHours(0, 0, 0, 0);
        return weekStart;
    }
    /**
     * 获取本周的结束时间（周日）
     */
    static getWeekEnd(): Date {
        const weekStart = this.getWeekStart();
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);
        weekEnd.setHours(23, 59, 59, 999);
        return weekEnd;
    }
    /**
     * 获取本月的开始时间
     */
    static getMonthStart(): Date {
        const today = new Date();
        return new Date(today.getFullYear(), today.getMonth(), 1, 0, 0, 0, 0);
    }
    /**
     * 获取本月的结束时间
     */
    static getMonthEnd(): Date {
        const today = new Date();
        return new Date(today.getFullYear(), today.getMonth() + 1, 0, 23, 59, 59, 999);
    }
    /**
     * 获取本年的开始时间
     */
    static getYearStart(): Date {
        const today = new Date();
        return new Date(today.getFullYear(), 0, 1, 0, 0, 0, 0);
    }
    /**
     * 获取本年的结束时间
     */
    static getYearEnd(): Date {
        const today = new Date();
        return new Date(today.getFullYear(), 11, 31, 23, 59, 59, 999);
    }
    /**
     * 获取相对时间描述
     */
    static getRelativeTime(date: Date): string {
        const now = new Date();
        const diff = now.getTime() - date.getTime();
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        if (days > 0) {
            if (days === 1)
                return '昨天';
            if (days === 2)
                return '前天';
            if (days < 7)
                return `${days}天前`;
            if (days < 30)
                return `${Math.floor(days / 7)}周前`;
            if (days < 365)
                return `${Math.floor(days / 30)}个月前`;
            return `${Math.floor(days / 365)}年前`;
        }
        if (hours > 0) {
            return `${hours}小时前`;
        }
        if (minutes > 0) {
            return `${minutes}分钟前`;
        }
        return '刚刚';
    }
    /**
     * 检查是否为今天
     */
    static isToday(date: Date): boolean {
        const today = new Date();
        return date.getDate() === today.getDate() &&
            date.getMonth() === today.getMonth() &&
            date.getFullYear() === today.getFullYear();
    }
    /**
     * 检查是否为本周
     */
    static isThisWeek(date: Date): boolean {
        const weekStart = this.getWeekStart();
        const weekEnd = this.getWeekEnd();
        return date >= weekStart && date <= weekEnd;
    }
    /**
     * 检查是否为本月
     */
    static isThisMonth(date: Date): boolean {
        const today = new Date();
        return date.getMonth() === today.getMonth() &&
            date.getFullYear() === today.getFullYear();
    }
}
