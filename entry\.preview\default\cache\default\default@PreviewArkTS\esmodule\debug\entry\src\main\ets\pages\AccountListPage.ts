if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface AccountListPage_Params {
    accounts?: Account[];
    isLoading?: boolean;
    searchText?: string;
    selectedType?: string;
    sortBy?: string;
    hasMore?: boolean;
    currentPage?: number;
    pageSize?: number;
    databaseService?: DatabaseService;
}
import type { Account } from '../models/Account';
import { AccountCard } from "@normalized:N&&&entry/src/main/ets/components/AccountCard&";
import { DatabaseService } from "@normalized:N&&&entry/src/main/ets/services/DatabaseService&";
import router from "@ohos:router";
class AccountListPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__accounts = new ObservedPropertyObjectPU([], this, "accounts");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__searchText = new ObservedPropertySimplePU('', this, "searchText");
        this.__selectedType = new ObservedPropertySimplePU('all', this, "selectedType");
        this.__sortBy = new ObservedPropertySimplePU('date', this, "sortBy");
        this.__hasMore = new ObservedPropertySimplePU(true, this, "hasMore");
        this.__currentPage = new ObservedPropertySimplePU(0, this, "currentPage");
        this.pageSize = 20;
        this.databaseService = DatabaseService.getInstance();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: AccountListPage_Params) {
        if (params.accounts !== undefined) {
            this.accounts = params.accounts;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.searchText !== undefined) {
            this.searchText = params.searchText;
        }
        if (params.selectedType !== undefined) {
            this.selectedType = params.selectedType;
        }
        if (params.sortBy !== undefined) {
            this.sortBy = params.sortBy;
        }
        if (params.hasMore !== undefined) {
            this.hasMore = params.hasMore;
        }
        if (params.currentPage !== undefined) {
            this.currentPage = params.currentPage;
        }
        if (params.pageSize !== undefined) {
            this.pageSize = params.pageSize;
        }
        if (params.databaseService !== undefined) {
            this.databaseService = params.databaseService;
        }
    }
    updateStateVars(params: AccountListPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__accounts.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__searchText.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedType.purgeDependencyOnElmtId(rmElmtId);
        this.__sortBy.purgeDependencyOnElmtId(rmElmtId);
        this.__hasMore.purgeDependencyOnElmtId(rmElmtId);
        this.__currentPage.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__accounts.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__searchText.aboutToBeDeleted();
        this.__selectedType.aboutToBeDeleted();
        this.__sortBy.aboutToBeDeleted();
        this.__hasMore.aboutToBeDeleted();
        this.__currentPage.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __accounts: ObservedPropertyObjectPU<Account[]>;
    get accounts() {
        return this.__accounts.get();
    }
    set accounts(newValue: Account[]) {
        this.__accounts.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __searchText: ObservedPropertySimplePU<string>;
    get searchText() {
        return this.__searchText.get();
    }
    set searchText(newValue: string) {
        this.__searchText.set(newValue);
    }
    private __selectedType: ObservedPropertySimplePU<string>; // all, income, expense
    get selectedType() {
        return this.__selectedType.get();
    }
    set selectedType(newValue: string) {
        this.__selectedType.set(newValue);
    }
    private __sortBy: ObservedPropertySimplePU<string>; // date, amount, category
    get sortBy() {
        return this.__sortBy.get();
    }
    set sortBy(newValue: string) {
        this.__sortBy.set(newValue);
    }
    private __hasMore: ObservedPropertySimplePU<boolean>;
    get hasMore() {
        return this.__hasMore.get();
    }
    set hasMore(newValue: boolean) {
        this.__hasMore.set(newValue);
    }
    private __currentPage: ObservedPropertySimplePU<number>;
    get currentPage() {
        return this.__currentPage.get();
    }
    set currentPage(newValue: number) {
        this.__currentPage.set(newValue);
    }
    private pageSize: number;
    private databaseService: DatabaseService;
    aboutToAppear() {
        this.loadAccounts(true);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AccountListPage.ets(25:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        // 顶部导航栏
        this.buildHeader.bind(this)();
        // 搜索和筛选栏
        this.buildSearchAndFilter.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 账单列表
            if (this.isLoading && this.accounts.length === 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.buildLoadingView.bind(this)();
                });
            }
            else if (this.accounts.length === 0) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.buildEmptyView.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.buildAccountList.bind(this)();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    buildHeader(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AccountListPage.ets(48:5)", "entry");
            Row.width('100%');
            Row.padding({ left: 16, right: 16, top: 12, bottom: 12 });
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 返回按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AccountListPage.ets(50:7)", "entry");
            // 返回按钮
            Row.width(40);
            // 返回按钮
            Row.height(40);
            // 返回按钮
            Row.borderRadius(20);
            // 返回按钮
            Row.justifyContent(FlexAlign.Center);
            // 返回按钮
            Row.alignItems(VerticalAlign.Center);
            // 返回按钮
            Row.onClick(() => {
                router.back();
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('←');
            Text.debugLine("entry/src/main/ets/pages/AccountListPage.ets(51:9)", "entry");
            Text.fontSize(20);
            Text.fontColor('#212121');
        }, Text);
        Text.pop();
        // 返回按钮
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('账单列表');
            Text.debugLine("entry/src/main/ets/pages/AccountListPage.ets(64:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 添加按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AccountListPage.ets(72:7)", "entry");
            // 添加按钮
            Row.width(40);
            // 添加按钮
            Row.height(40);
            // 添加按钮
            Row.borderRadius(20);
            // 添加按钮
            Row.justifyContent(FlexAlign.Center);
            // 添加按钮
            Row.alignItems(VerticalAlign.Center);
            // 添加按钮
            Row.onClick(() => {
                router.pushUrl({
                    url: 'pages/AddAccountPage'
                });
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('+');
            Text.debugLine("entry/src/main/ets/pages/AccountListPage.ets(73:9)", "entry");
            Text.fontSize(20);
            Text.fontColor('#212121');
        }, Text);
        Text.pop();
        // 添加按钮
        Row.pop();
        Row.pop();
    }
    buildSearchAndFilter(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AccountListPage.ets(95:5)", "entry");
            Column.width('100%');
            Column.padding(16);
            Column.backgroundColor('#FFFFFF');
            Column.margin({ bottom: 8 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 搜索框
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AccountListPage.ets(97:7)", "entry");
            // 搜索框
            Row.width('100%');
            // 搜索框
            Row.margin({ bottom: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '搜索账单...', text: this.searchText });
            TextInput.debugLine("entry/src/main/ets/pages/AccountListPage.ets(98:9)", "entry");
            TextInput.layoutWeight(1);
            TextInput.backgroundColor('#F5F5F5');
            TextInput.borderRadius(8);
            TextInput.onChange((value: string) => {
                this.searchText = value;
                this.debounceSearch();
            });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🔍');
            Text.debugLine("entry/src/main/ets/pages/AccountListPage.ets(107:9)", "entry");
            Text.fontSize(16);
            Text.margin({ left: 8 });
            Text.onClick(() => {
                this.performSearch();
            });
        }, Text);
        Text.pop();
        // 搜索框
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 筛选和排序
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AccountListPage.ets(118:7)", "entry");
            // 筛选和排序
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 类型筛选
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AccountListPage.ets(120:9)", "entry");
            // 类型筛选
            Row.layoutWeight(1);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('类型:');
            Text.debugLine("entry/src/main/ets/pages/AccountListPage.ets(121:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#757575');
            Text.margin({ right: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getTypeText());
            Text.debugLine("entry/src/main/ets/pages/AccountListPage.ets(126:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#1976D2');
            Text.onClick(() => {
                this.showTypeSelector();
            });
        }, Text);
        Text.pop();
        // 类型筛选
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 排序
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AccountListPage.ets(136:9)", "entry");
            // 排序
            Row.layoutWeight(1);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('排序:');
            Text.debugLine("entry/src/main/ets/pages/AccountListPage.ets(137:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#757575');
            Text.margin({ right: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getSortText());
            Text.debugLine("entry/src/main/ets/pages/AccountListPage.ets(142:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#1976D2');
            Text.onClick(() => {
                this.showSortSelector();
            });
        }, Text);
        Text.pop();
        // 排序
        Row.pop();
        // 筛选和排序
        Row.pop();
        Column.pop();
    }
    buildLoadingView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AccountListPage.ets(161:5)", "entry");
            Column.width('100%');
            Column.layoutWeight(1);
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('⏳');
            Text.debugLine("entry/src/main/ets/pages/AccountListPage.ets(162:7)", "entry");
            Text.fontSize(48);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('加载中...');
            Text.debugLine("entry/src/main/ets/pages/AccountListPage.ets(166:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#757575');
        }, Text);
        Text.pop();
        Column.pop();
    }
    buildEmptyView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AccountListPage.ets(178:5)", "entry");
            Column.width('100%');
            Column.layoutWeight(1);
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📝');
            Text.debugLine("entry/src/main/ets/pages/AccountListPage.ets(179:7)", "entry");
            Text.fontSize(48);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('暂无账单记录');
            Text.debugLine("entry/src/main/ets/pages/AccountListPage.ets(183:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#757575');
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('点击右上角"+"添加第一笔记录');
            Text.debugLine("entry/src/main/ets/pages/AccountListPage.ets(188:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#9E9E9E');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('立即添加');
            Button.debugLine("entry/src/main/ets/pages/AccountListPage.ets(192:7)", "entry");
            Button.width(120);
            Button.height(40);
            Button.fontSize(14);
            Button.backgroundColor('#1976D2');
            Button.borderRadius(20);
            Button.margin({ top: 20 });
            Button.onClick(() => {
                router.pushUrl({
                    url: 'pages/AddAccountPage'
                });
            });
        }, Button);
        Button.pop();
        Column.pop();
    }
    buildAccountList(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/AccountListPage.ets(213:5)", "entry");
            Scroll.layoutWeight(1);
            Scroll.onReachEnd(() => {
                if (this.hasMore && !this.isLoading) {
                    this.loadMore();
                }
            });
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AccountListPage.ets(214:7)", "entry");
            Column.padding(16);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = (_item, index: number) => {
                const account = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    __Common__.create();
                    __Common__.margin({ bottom: 8 });
                }, __Common__);
                {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        if (isInitialRender) {
                            let componentCall = new AccountCard(this, {
                                account: account,
                                showDate: true,
                                onItemClick: (account: Account): void => this.handleAccountClick(account),
                                onItemLongPress: (account: Account): void => this.handleAccountLongPress(account)
                            }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/AccountListPage.ets", line: 216, col: 11 });
                            ViewPU.create(componentCall);
                            let paramsLambda = () => {
                                return {
                                    account: account,
                                    showDate: true,
                                    onItemClick: (account: Account): void => this.handleAccountClick(account),
                                    onItemLongPress: (account: Account): void => this.handleAccountLongPress(account)
                                };
                            };
                            componentCall.paramsGenerator_ = paramsLambda;
                        }
                        else {
                            this.updateStateVarsOfChildByElmtId(elmtId, {
                                account: account,
                                showDate: true
                            });
                        }
                    }, { name: "AccountCard" });
                }
                __Common__.pop();
            };
            this.forEachUpdateFunction(elmtId, this.accounts, forEachItemGenFunction, undefined, true, false);
        }, ForEach);
        ForEach.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 加载更多指示器
            if (this.hasMore) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/AccountListPage.ets(227:11)", "entry");
                        Row.width('100%');
                        Row.height(50);
                        Row.justifyContent(FlexAlign.Center);
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.isLoading) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create('加载中...');
                                    Text.debugLine("entry/src/main/ets/pages/AccountListPage.ets(229:15)", "entry");
                                    Text.fontSize(14);
                                    Text.fontColor('#757575');
                                }, Text);
                                Text.pop();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create('上拉加载更多');
                                    Text.debugLine("entry/src/main/ets/pages/AccountListPage.ets(233:15)", "entry");
                                    Text.fontSize(14);
                                    Text.fontColor('#1976D2');
                                    Text.onClick(() => {
                                        this.loadMore();
                                    });
                                }, Text);
                                Text.pop();
                            });
                        }
                    }, If);
                    If.pop();
                    Row.pop();
                });
            }
            else if (this.accounts.length > 0) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('没有更多数据了');
                        Text.debugLine("entry/src/main/ets/pages/AccountListPage.ets(245:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#9E9E9E');
                        Text.textAlign(TextAlign.Center);
                        Text.width('100%');
                        Text.height(50);
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
        Scroll.pop();
    }
    // 事件处理方法
    private async loadAccounts(refresh: boolean = false): Promise<void> {
        if (this.isLoading)
            return;
        this.isLoading = true;
        try {
            if (refresh) {
                this.currentPage = 0;
                this.accounts = [];
            }
            const offset = this.currentPage * this.pageSize;
            const newAccounts = await this.databaseService.getAccounts(this.pageSize, offset);
            if (refresh) {
                this.accounts = newAccounts;
            }
            else {
                this.accounts = this.accounts.concat(newAccounts);
            }
            this.hasMore = newAccounts.length === this.pageSize;
            this.currentPage++;
            // 应用筛选和排序
            this.applyFiltersAndSort();
        }
        catch (error) {
            console.error('加载账单失败:', error);
        }
        finally {
            this.isLoading = false;
        }
    }
    private loadMore(): void {
        this.loadAccounts(false);
    }
    private handleAccountClick(account: Account): void {
        console.log('点击账单:', account.id);
        router.pushUrl({
            url: 'pages/AccountDetailPage',
            params: {
                accountId: account.id
            }
        });
    }
    private handleAccountLongPress(account: Account): void {
        console.log('长按账单:', account.id);
        // TODO: 显示操作菜单
    }
    private debounceSearch(): void {
        // 简单的防抖实现
        setTimeout(() => {
            this.performSearch();
        }, 500);
    }
    private performSearch(): void {
        console.log('搜索:', this.searchText);
        this.loadAccounts(true);
    }
    private showTypeSelector(): void {
        console.log('显示类型选择器');
        // TODO: 显示类型选择对话框
    }
    private showSortSelector(): void {
        console.log('显示排序选择器');
        // TODO: 显示排序选择对话框
    }
    private applyFiltersAndSort(): void {
        let filteredAccounts = [...this.accounts];
        // 应用类型筛选
        if (this.selectedType !== 'all') {
            filteredAccounts = filteredAccounts.filter(account => account.type === this.selectedType);
        }
        // 应用搜索筛选
        if (this.searchText) {
            const searchLower = this.searchText.toLowerCase();
            filteredAccounts = filteredAccounts.filter(account => account.description.toLowerCase().includes(searchLower) ||
                account.category.toLowerCase().includes(searchLower));
        }
        // 应用排序
        filteredAccounts.sort((a, b) => {
            switch (this.sortBy) {
                case 'amount':
                    return b.amount - a.amount;
                case 'category':
                    return a.category.localeCompare(b.category);
                case 'date':
                default:
                    return b.date.getTime() - a.date.getTime();
            }
        });
        this.accounts = filteredAccounts;
    }
    private getTypeText(): string {
        switch (this.selectedType) {
            case 'income': return '收入';
            case 'expense': return '支出';
            default: return '全部';
        }
    }
    private getSortText(): string {
        switch (this.sortBy) {
            case 'amount': return '金额';
            case 'category': return '分类';
            default: return '时间';
        }
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "AccountListPage";
    }
}
registerNamedRoute(() => new AccountListPage(undefined, {}), "", { bundleName: "com.silence.monter", moduleName: "entry", pagePath: "pages/AccountListPage", pageFullPath: "entry/src/main/ets/pages/AccountListPage", integratedHsp: "false", moduleType: "followWithHap" });
