# MonterAI - 智能记账助手

## 🎯 项目简介

MonterAI 是一款基于 HarmonyOS API19 开发的创新AI记账工具，专为参加华为开发者大赛而设计。应用集成了多项AI技术，提供智能化的记账体验，帮助用户更好地管理个人财务。

## ✨ 核心特性

### 🤖 AI智能记账
- **拍照识别**: 使用OCR技术识别小票、发票，自动提取金额和商品信息
- **语音记账**: 支持自然语言语音输入，AI理解并自动分类记账
- **智能分类**: AI自动识别消费类型，提供分类建议

### 📊 智能分析
- **消费习惯分析**: AI分析用户消费模式，提供个性化洞察
- **预算管理**: 智能预算建议和超支提醒
- **理财建议**: 基于消费数据的个性化理财建议

### 🔄 分布式协同
- **多设备同步**: 利用HarmonyOS分布式特性，手机、平板、手表数据实时同步
- **设备管理**: 统一管理所有登录设备
- **离线支持**: 支持离线记账，联网后自动同步

### 🎨 现代化界面
- **Material Design**: 遵循现代设计规范
- **深色模式**: 支持浅色/深色/跟随系统主题
- **流畅动画**: 丰富的交互动画效果

## 🏗️ 技术架构

### 开发环境
- **平台**: HarmonyOS API19
- **开发语言**: ArkTS
- **开发工具**: DevEco Studio

### 核心技术
- **AI服务**: OCR识别、语音识别、自然语言处理
- **分布式数据库**: HarmonyOS分布式数据管理
- **设备协同**: HarmonyOS分布式硬件管理
- **本地存储**: 关系型数据库存储

### 项目结构
```
MonterAI/
├── entry/src/main/ets/
│   ├── models/              # 数据模型
│   │   ├── Account.ets      # 账单模型
│   │   ├── Category.ets     # 分类模型
│   │   └── User.ets         # 用户模型
│   ├── services/            # 服务层
│   │   ├── AIService.ets    # AI服务
│   │   ├── DatabaseService.ets # 数据库服务
│   │   └── SyncService.ets  # 同步服务
│   ├── pages/               # 页面
│   │   ├── Index.ets        # 主页
│   │   ├── AddAccountPage.ets # 添加账单
│   │   ├── PhotoScanPage.ets # 拍照识别
│   │   ├── VoiceInputPage.ets # 语音记账
│   │   ├── AnalysisPage.ets # 智能分析
│   │   └── SettingsPage.ets # 设置页面
│   ├── components/          # 组件
│   │   ├── AccountCard.ets  # 账单卡片
│   │   ├── SummaryCard.ets  # 统计卡片
│   │   └── QuickActionBar.ets # 快速操作栏
│   └── common/              # 公共模块
│       ├── constants/       # 常量
│       └── utils/           # 工具类
```

## 🚀 功能特色

### 1. 智能拍照识别
- 支持小票、发票、收据识别
- 自动提取商家名称、金额、商品明细
- AI置信度评估，确保识别准确性
- 支持多种图片格式

### 2. 语音智能记账
- 自然语言理解："今天午餐花了25块5"
- 自动识别金额、分类、描述信息
- 支持方言和口语化表达
- 实时语音反馈

### 3. AI消费分析
- 消费趋势分析和预测
- 分类支出占比统计
- 个性化消费洞察
- 智能预算建议

### 4. 分布式多设备协同
- 手机、平板、手表数据实时同步
- 设备状态监控和管理
- 冲突解决和数据一致性保证
- 离线数据缓存和同步

## 📱 界面展示

### 主界面
- 账单概览和统计图表
- 快速记账入口
- 最近记录列表
- 智能分析入口

### 添加账单
- 收入/支出类型切换
- 分类图标选择
- 金额和备注输入
- 日期时间选择

### AI功能
- 拍照识别界面
- 语音录制界面
- 识别结果确认
- 智能分析报告

## 🔧 安装和运行

### 环境要求
- HarmonyOS 4.0+ 设备
- DevEco Studio 4.0+
- Node.js 16+

### 安装步骤
1. 克隆项目代码
```bash
git clone [项目地址]
cd MonterAI
```

2. 安装依赖
```bash
npm install
```

3. 在DevEco Studio中打开项目

4. 连接HarmonyOS设备或启动模拟器

5. 编译并运行项目

## 🧪 测试

### 单元测试
```bash
npm test
```

### 功能测试
- 账单CRUD操作测试
- AI识别功能测试
- 分布式同步测试
- 界面交互测试

## 🎯 创新亮点

### 1. AI技术集成
- 多模态AI输入（语音+图像）
- 智能消费分析和预测
- 个性化理财建议

### 2. HarmonyOS特性
- 分布式数据同步
- 多设备协同体验
- 原生性能优化

### 3. 用户体验
- 零学习成本的AI交互
- 流畅的多设备切换
- 个性化的智能建议

## 🏆 大赛优势

### 技术创新
- 首创AI多模态记账方式
- 深度集成HarmonyOS分布式能力
- 创新的消费分析算法

### 实用价值
- 解决传统记账繁琐问题
- 提供智能理财建议
- 支持多设备无缝体验

### 市场前景
- 个人理财市场需求巨大
- AI技术应用前景广阔
- HarmonyOS生态发展机遇

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 👥 开发团队

- 项目负责人: [姓名]
- AI算法工程师: [姓名]
- HarmonyOS开发工程师: [姓名]
- UI/UX设计师: [姓名]

## 📞 联系我们

- 邮箱: [邮箱地址]
- 微信: [微信号]
- GitHub: [GitHub地址]

---

**MonterAI - 让记账变得智能简单** 🚀
