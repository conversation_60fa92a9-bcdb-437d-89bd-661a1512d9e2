/**
 * 应用常量定义
 */
export class AppConstants {
    // 应用信息
    static readonly APP_NAME = 'MonterAI';
    static readonly APP_VERSION = '1.0.0';
    // 数据库相关
    static readonly DB_NAME = 'monter_ai.db';
    static readonly DB_VERSION = 1;
    // 数据库表名
    static readonly TABLE_ACCOUNTS = 'accounts';
    static readonly TABLE_CATEGORIES = 'categories';
    static readonly TABLE_USERS = 'users';
    // 表名
    static readonly TABLE_ACCOUNTS = 'accounts';
    static readonly TABLE_CATEGORIES = 'categories';
    static readonly TABLE_USERS = 'users';
    static readonly TABLE_BUDGETS = 'budgets';
    // 存储键名
    static readonly STORAGE_USER_SETTINGS = 'user_settings';
    static readonly STORAGE_LAST_SYNC = 'last_sync';
    static readonly STORAGE_APP_CONFIG = 'app_config';
    // AI服务相关
    static readonly AI_OCR_CONFIDENCE_THRESHOLD = 0.7;
    static readonly AI_VOICE_TIMEOUT = 10000; // 10秒
    static readonly AI_ANALYSIS_CACHE_TIME = 3600000; // 1小时
    // 网络相关
    static readonly REQUEST_TIMEOUT = 30000; // 30秒
    static readonly MAX_RETRY_COUNT = 3;
    // 文件相关
    static readonly MAX_IMAGE_SIZE = 5 * 1024 * 1024; // 5MB
    static readonly SUPPORTED_IMAGE_FORMATS = ['jpg', 'jpeg', 'png', 'webp'];
    // 分页相关
    static readonly DEFAULT_PAGE_SIZE = 20;
    static readonly MAX_PAGE_SIZE = 100;
    // 日期格式
    static readonly DATE_FORMAT = 'YYYY-MM-DD';
    static readonly DATETIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';
    static readonly TIME_FORMAT = 'HH:mm';
    // 货币格式
    static readonly CURRENCY_SYMBOL = '¥';
    static readonly DECIMAL_PLACES = 2;
    // 颜色主题
    static readonly PRIMARY_COLOR = '#1976D2';
    static readonly SECONDARY_COLOR = '#424242';
    static readonly SUCCESS_COLOR = '#4CAF50';
    static readonly WARNING_COLOR = '#FF9800';
    static readonly ERROR_COLOR = '#F44336';
    static readonly INFO_COLOR = '#2196F3';
    static readonly INCOME_COLOR = '#4CAF50';
    static readonly EXPENSE_COLOR = '#F44336';
    static readonly BACKGROUND_COLOR = '#FAFAFA';
    static readonly SURFACE_COLOR = '#FFFFFF';
    static readonly TEXT_PRIMARY_COLOR = '#212121';
    static readonly TEXT_SECONDARY_COLOR = '#757575';
    // 动画时长
    static readonly ANIMATION_SHORT = 200;
    static readonly ANIMATION_MEDIUM = 300;
    static readonly ANIMATION_LONG = 500;
    // 路由路径
    static readonly ROUTE_MAIN = '/pages/MainPage';
    static readonly ROUTE_ADD_ACCOUNT = '/pages/AddAccountPage';
    static readonly ROUTE_ANALYSIS = '/pages/AnalysisPage';
    static readonly ROUTE_SETTINGS = '/pages/SettingsPage';
    static readonly ROUTE_CATEGORY_MANAGE = '/pages/CategoryManagePage';
    static readonly ROUTE_BUDGET_MANAGE = '/pages/BudgetManagePage';
    // 权限相关
    static readonly PERMISSION_CAMERA = 'ohos.permission.CAMERA';
    static readonly PERMISSION_MICROPHONE = 'ohos.permission.MICROPHONE';
    static readonly PERMISSION_READ_MEDIA = 'ohos.permission.READ_MEDIA';
    static readonly PERMISSION_WRITE_MEDIA = 'ohos.permission.WRITE_MEDIA';
    static readonly PERMISSION_INTERNET = 'ohos.permission.INTERNET';
    static readonly PERMISSION_LOCATION = 'ohos.permission.LOCATION';
    // 事件名称
    static readonly EVENT_ACCOUNT_ADDED = 'account_added';
    static readonly EVENT_ACCOUNT_UPDATED = 'account_updated';
    static readonly EVENT_ACCOUNT_DELETED = 'account_deleted';
    static readonly EVENT_CATEGORY_CHANGED = 'category_changed';
    static readonly EVENT_SETTINGS_CHANGED = 'settings_changed';
    static readonly EVENT_SYNC_COMPLETED = 'sync_completed';
}
