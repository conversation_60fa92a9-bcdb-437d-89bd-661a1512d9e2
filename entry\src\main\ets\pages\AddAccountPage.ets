import { Account, AccountType } from '../models/Account';
import { Category, DefaultCategories, CategoryType } from '../models/Category';
import { FormatUtils } from '../common/utils/FormatUtils';
import { DatabaseService } from '../services/DatabaseService';
import router from '@ohos.router';

@Entry
@Component
struct AddAccountPage {
  @State account: Account = new Account();
  @State categories: Category[] = [];
  @State selectedCategory: Category | null = null;
  @State amount: string = '';
  @State description: string = '';
  @State accountType: AccountType = AccountType.EXPENSE;
  @State showDatePicker: boolean = false;
  @State isEdit: boolean = false;
  @State isSaving: boolean = false;

  private databaseService: DatabaseService = DatabaseService.getInstance();

  aboutToAppear() {
    // 获取路由参数
    const params = router.getParams() as Record<string, Object>;
    if (params && params['type']) {
      this.accountType = params['type'] as AccountType;
    }

    // 检查是否为编辑模式
    if (params && params['account']) {
      this.isEdit = true;
      const editAccount = params['account'] as Account;
      this.loadAccountForEdit(editAccount);
    }

    this.loadCategories();
  }

  build() {
    Column() {
      // 顶部导航栏
      this.buildHeader()

      // 内容区域
      Scroll() {
        Column() {
          // 账单类型切换
          this.buildTypeSelector()

          // 金额输入
          this.buildAmountInput()

          // 分类选择
          this.buildCategorySelector()

          // 描述输入
          this.buildDescriptionInput()

          // 日期选择
          this.buildDateSelector()

          // 保存按钮
          this.buildSaveButton()
        }
        .padding(16)
      }
      .layoutWeight(1)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder
  buildHeader() {
    Row() {
      // 返回按钮
      Row() {
        Text('←')
          .fontSize(20)
          .fontColor('#212121')
      }
      .width(40)
      .height(40)
      .borderRadius(20)
      .justifyContent(FlexAlign.Center)
      .alignItems(VerticalAlign.Center)
      .onClick(() => {
        router.back();
      })

      Text(this.getPageTitle())
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .fontColor('#212121')
        .layoutWeight(1)
        .textAlign(TextAlign.Center)

      // 占位
      Row()
        .width(40)
        .height(40)
    }
    .width('100%')
    .padding({ left: 16, right: 16, top: 12, bottom: 12 })
    .backgroundColor('#FFFFFF')
  }

  @Builder
  buildTypeSelector() {
    Row() {
      // 支出
      Row() {
        Text('支出')
          .fontSize(16)
          .fontColor(this.accountType === AccountType.EXPENSE ? '#FFFFFF' : '#212121')
      }
      .layoutWeight(1)
      .height(44)
      .borderRadius(22)
      .backgroundColor(this.accountType === AccountType.EXPENSE ? '#F44336' : '#FFFFFF')
      .justifyContent(FlexAlign.Center)
      .alignItems(VerticalAlign.Center)
      .onClick(() => {
        this.accountType = AccountType.EXPENSE;
        this.loadCategories();
      })

      // 收入
      Row() {
        Text('收入')
          .fontSize(16)
          .fontColor(this.accountType === AccountType.INCOME ? '#FFFFFF' : '#212121')
      }
      .layoutWeight(1)
      .height(44)
      .borderRadius(22)
      .backgroundColor(this.accountType === AccountType.INCOME ? '#4CAF50' : '#FFFFFF')
      .justifyContent(FlexAlign.Center)
      .alignItems(VerticalAlign.Center)
      .onClick(() => {
        this.accountType = AccountType.INCOME;
        this.loadCategories();
      })
    }
    .width('100%')
    .padding(4)
    .backgroundColor('#F0F0F0')
    .borderRadius(26)
    .margin({ bottom: 20 })
  }

  @Builder
  buildAmountInput() {
    Column() {
      Text('金额')
        .fontSize(16)
        .fontWeight(FontWeight.Medium)
        .fontColor('#212121')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 8 })

      TextInput({ placeholder: '请输入金额', text: this.amount })
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .type(InputType.Number)
        .backgroundColor('#FFFFFF')
        .borderRadius(8)
        .padding(16)
        .onChange((value: string) => {
          this.amount = value;
        })
    }
    .width('100%')
    .margin({ bottom: 20 })
  }

  @Builder
  buildCategorySelector() {
    Column() {
      Text('分类')
        .fontSize(16)
        .fontWeight(FontWeight.Medium)
        .fontColor('#212121')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 8 })

      // 分类网格
      Grid() {
        ForEach(this.categories, (category: Category) => {
          GridItem() {
            Column() {
              Text(category.icon)
                .fontSize(24)
                .margin({ bottom: 4 })

              Text(category.name)
                .fontSize(12)
                .fontColor('#212121')
                .maxLines(1)
                .textOverflow({ overflow: TextOverflow.Ellipsis })
            }
            .width('100%')
            .height(80)
            .borderRadius(8)
            .backgroundColor(this.selectedCategory?.id === category.id ? category.color : '#FFFFFF')
            .justifyContent(FlexAlign.Center)
            .alignItems(HorizontalAlign.Center)
            .onClick(() => {
              this.selectedCategory = category;
            })
          }
        })
      }
      .columnsTemplate('1fr 1fr 1fr 1fr')
      .rowsGap(8)
      .columnsGap(8)
      .height(180)
    }
    .width('100%')
    .margin({ bottom: 20 })
  }

  @Builder
  buildDescriptionInput() {
    Column() {
      Text('备注')
        .fontSize(16)
        .fontWeight(FontWeight.Medium)
        .fontColor('#212121')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 8 })

      TextInput({ placeholder: '请输入备注信息（可选）', text: this.description })
        .fontSize(16)
        .backgroundColor('#FFFFFF')
        .borderRadius(8)
        .padding(16)
        .onChange((value: string) => {
          this.description = value;
        })
    }
    .width('100%')
    .margin({ bottom: 20 })
  }

  @Builder
  buildDateSelector() {
    Column() {
      Text('日期')
        .fontSize(16)
        .fontWeight(FontWeight.Medium)
        .fontColor('#212121')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 8 })

      Row() {
        Text(this.formatDate(this.account.date))
          .fontSize(16)
          .fontColor('#212121')
          .layoutWeight(1)

        Text('📅')
          .fontSize(16)
      }
      .width('100%')
      .height(48)
      .backgroundColor('#FFFFFF')
      .borderRadius(8)
      .padding({ left: 16, right: 16 })
      .justifyContent(FlexAlign.SpaceBetween)
      .alignItems(VerticalAlign.Center)
      .onClick(() => {
        this.showDatePicker = true;
      })
    }
    .width('100%')
    .margin({ bottom: 30 })
  }

  @Builder
  buildSaveButton() {
    Button(this.isSaving ? '保存中...' : (this.isEdit ? '更新' : '保存'))
      .width('100%')
      .height(48)
      .fontSize(16)
      .fontWeight(FontWeight.Bold)
      .backgroundColor(this.canSave() && !this.isSaving ? '#1976D2' : '#CCCCCC')
      .borderRadius(8)
      .enabled(this.canSave() && !this.isSaving)
      .onClick(() => {
        this.saveAccount();
      })
  }

  // 私有方法
  private loadCategories() {
    if (this.accountType === AccountType.EXPENSE) {
      this.categories = DefaultCategories.getExpenseCategories();
    } else {
      this.categories = DefaultCategories.getIncomeCategories();
    }
    this.selectedCategory = null;
  }

  private canSave(): boolean {
    return this.amount.length > 0 && 
           parseFloat(this.amount) > 0 && 
           this.selectedCategory !== null;
  }

  private async saveAccount(): Promise<void> {
    if (!this.canSave() || this.isSaving) return;

    this.isSaving = true;

    try {
      this.account.amount = parseFloat(this.amount);
      this.account.type = this.accountType;
      this.account.category = this.selectedCategory!.name;
      this.account.description = this.description;

      console.log('保存账单:', this.account);

      const success = await this.databaseService.saveAccount(this.account);

      if (success) {
        console.log('账单保存成功');
        router.back();
      } else {
        console.error('账单保存失败');
        // TODO: 显示错误提示
      }

    } catch (error) {
      console.error('保存账单时出错:', error);
      // TODO: 显示错误提示
    } finally {
      this.isSaving = false;
    }
  }

  private formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  private loadAccountForEdit(editAccount: Account): void {
    this.account = editAccount;
    this.amount = editAccount.amount.toString();
    this.description = editAccount.description || '';
    this.accountType = editAccount.type;

    // 查找对应的分类
    const categories = this.accountType === AccountType.EXPENSE ?
      DefaultCategories.getExpenseCategories() :
      DefaultCategories.getIncomeCategories();

    this.selectedCategory = categories.find(cat => cat.name === editAccount.category) || null;
  }

  private getPageTitle(): string {
    if (this.isEdit) {
      return this.accountType === AccountType.EXPENSE ? '编辑支出' : '编辑收入';
    } else {
      return this.accountType === AccountType.EXPENSE ? '添加支出' : '添加收入';
    }
  }
}
