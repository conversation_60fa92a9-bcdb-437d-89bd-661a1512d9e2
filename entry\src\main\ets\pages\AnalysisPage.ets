import { Account, AccountType } from '../models/Account';
import { AIService, SpendingAnalysis, CategoryBreakdown, TrendData } from '../services/AIService';
import { FormatUtils } from '../common/utils/FormatUtils';
import { SimplePieChart, ChartData, ChartUtils } from '../components/PieChart';
import router from '@ohos.router';

@Entry
@Component
struct AnalysisPage {
  @State analysis: SpendingAnalysis | null = null;
  @State isLoading: boolean = false;
  @State selectedPeriod: string = '本月';
  @State accounts: Account[] = [];

  private aiService: AIService = AIService.getInstance();

  aboutToAppear() {
    this.loadAnalysis();
  }

  build() {
    Column() {
      // 顶部导航栏
      this.buildHeader()

      // 内容区域
      if (this.isLoading) {
        this.buildLoadingView()
      } else if (this.analysis) {
        this.buildAnalysisContent()
      } else {
        this.buildEmptyView()
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder
  buildHeader() {
    Row() {
      // 返回按钮
      Row() {
        Text('←')
          .fontSize(20)
          .fontColor('#212121')
      }
      .width(40)
      .height(40)
      .borderRadius(20)
      .justifyContent(FlexAlign.Center)
      .alignItems(VerticalAlign.Center)
      .onClick(() => {
        router.back();
      })

      Text('智能分析')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .fontColor('#212121')
        .layoutWeight(1)
        .textAlign(TextAlign.Center)

      // 刷新按钮
      Row() {
        Text('🔄')
          .fontSize(16)
          .fontColor('#212121')
      }
      .width(40)
      .height(40)
      .borderRadius(20)
      .justifyContent(FlexAlign.Center)
      .alignItems(VerticalAlign.Center)
      .onClick(() => {
        this.loadAnalysis();
      })
    }
    .width('100%')
    .padding({ left: 16, right: 16, top: 12, bottom: 12 })
    .backgroundColor('#FFFFFF')
  }

  @Builder
  buildLoadingView() {
    Column() {
      Text('🤖')
        .fontSize(60)
        .margin({ bottom: 16 })

      Text('AI正在分析中...')
        .fontSize(16)
        .fontColor('#757575')
    }
    .width('100%')
    .layoutWeight(1)
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  @Builder
  buildEmptyView() {
    Column() {
      Text('📊')
        .fontSize(60)
        .margin({ bottom: 16 })

      Text('暂无数据')
        .fontSize(16)
        .fontColor('#757575')
        .margin({ bottom: 8 })

      Text('添加一些记录后再来查看分析吧')
        .fontSize(14)
        .fontColor('#9E9E9E')
    }
    .width('100%')
    .layoutWeight(1)
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  @Builder
  buildAnalysisContent() {
    Scroll() {
      Column() {
        // 总支出卡片
        this.buildTotalExpenseCard()

        // 分类支出分析
        this.buildCategoryAnalysis()

        // 分类支出图表
        this.buildCategoryChart()

        // 趋势分析
        this.buildTrendAnalysis()

        // AI洞察
        this.buildInsights()

        // AI建议
        this.buildRecommendations()
      }
      .padding(16)
    }
    .layoutWeight(1)
  }

  @Builder
  buildTotalExpenseCard() {
    Column() {
      Text(`${this.selectedPeriod}总支出`)
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .fontColor('#212121')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 8 })

      Text(FormatUtils.formatAmount(this.analysis!.totalExpense))
        .fontSize(32)
        .fontWeight(FontWeight.Bold)
        .fontColor('#F44336')
        .margin({ bottom: 16 })

      Row() {
        Text('💡')
          .fontSize(16)
          .margin({ right: 8 })

        Text('AI分析了您的消费习惯')
          .fontSize(14)
          .fontColor('#757575')
      }
    }
    .width('100%')
    .padding(20)
    .backgroundColor('#FFFFFF')
    .borderRadius(12)
    .margin({ bottom: 16 })
  }

  @Builder
  buildCategoryAnalysis() {
    Column() {
      Text('分类支出分析')
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .fontColor('#212121')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 16 })

      ForEach(this.analysis!.categoryBreakdown, (item: CategoryBreakdown, index: number) => {
        Column() {
          Row() {
            Text(this.getCategoryIcon(item.category))
              .fontSize(20)
              .margin({ right: 12 })

            Column() {
              Text(item.category)
                .fontSize(14)
                .fontColor('#212121')
                .alignSelf(ItemAlign.Start)

              Text(FormatUtils.formatAmount(item.amount))
                .fontSize(16)
                .fontWeight(FontWeight.Bold)
                .fontColor('#212121')
                .alignSelf(ItemAlign.Start)
            }
            .layoutWeight(1)
            .alignItems(HorizontalAlign.Start)

            Text(`${item.percentage.toFixed(1)}%`)
              .fontSize(14)
              .fontColor('#757575')
          }
          .width('100%')
          .margin({ bottom: 8 })

          // 进度条
          Row() {
            Row()
              .width(`${item.percentage}%`)
              .height(4)
              .backgroundColor(this.getCategoryColor(item.category))
              .borderRadius(2)
          }
          .width('100%')
          .height(4)
          .backgroundColor('#E0E0E0')
          .borderRadius(2)
        }
        .width('100%')
        .margin({ bottom: index < this.analysis!.categoryBreakdown.length - 1 ? 16 : 0 })
      })
    }
    .width('100%')
    .padding(20)
    .backgroundColor('#FFFFFF')
    .borderRadius(12)
    .margin({ bottom: 16 })
  }

  @Builder
  buildCategoryChart() {
    Column() {
      Text('支出分布')
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .fontColor('#212121')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 16 })

      if (this.analysis!.categoryBreakdown.length > 0) {
        SimplePieChart({
          data: this.getCategoryChartData(),
          size: 200
        })
      } else {
        Column() {
          Text('📊')
            .fontSize(48)
            .margin({ bottom: 12 })

          Text('暂无数据')
            .fontSize(14)
            .fontColor('#757575')
        }
        .width('100%')
        .height(200)
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)
      }
    }
    .width('100%')
    .padding(20)
    .backgroundColor('#FFFFFF')
    .borderRadius(12)
    .margin({ bottom: 16 })
  }

  @Builder
  buildTrendAnalysis() {
    Column() {
      Text('趋势分析')
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .fontColor('#212121')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 16 })

      ForEach(this.analysis!.trends, (trend: TrendData, index: number) => {
        Row() {
          Text(trend.period)
            .fontSize(14)
            .fontColor('#757575')
            .width(60)

          Text(FormatUtils.formatAmount(trend.amount))
            .fontSize(14)
            .fontColor('#212121')
            .layoutWeight(1)

          Row() {
            Text(trend.change > 0 ? '📈' : '📉')
              .fontSize(14)
              .margin({ right: 4 })

            Text(`${Math.abs(trend.change)}%`)
              .fontSize(14)
              .fontColor(trend.change > 0 ? '#F44336' : '#4CAF50')
          }
        }
        .width('100%')
        .margin({ bottom: index < this.analysis!.trends.length - 1 ? 12 : 0 })
      })
    }
    .width('100%')
    .padding(20)
    .backgroundColor('#FFFFFF')
    .borderRadius(12)
    .margin({ bottom: 16 })
  }

  @Builder
  buildInsights() {
    Column() {
      Row() {
        Text('🔍')
          .fontSize(20)
          .margin({ right: 8 })

        Text('AI洞察')
          .fontSize(16)
          .fontWeight(FontWeight.Bold)
          .fontColor('#212121')
      }
      .alignSelf(ItemAlign.Start)
      .margin({ bottom: 16 })

      ForEach(this.analysis!.insights, (insight: string, index: number) => {
        Row() {
          Text('•')
            .fontSize(16)
            .fontColor('#1976D2')
            .margin({ right: 8 })

          Text(insight)
            .fontSize(14)
            .fontColor('#212121')
            .layoutWeight(1)
        }
        .width('100%')
        .alignItems(VerticalAlign.Top)
        .margin({ bottom: index < this.analysis!.insights.length - 1 ? 8 : 0 })
      })
    }
    .width('100%')
    .padding(20)
    .backgroundColor('#E3F2FD')
    .borderRadius(12)
    .margin({ bottom: 16 })
  }

  @Builder
  buildRecommendations() {
    Column() {
      Row() {
        Text('💡')
          .fontSize(20)
          .margin({ right: 8 })

        Text('AI建议')
          .fontSize(16)
          .fontWeight(FontWeight.Bold)
          .fontColor('#212121')
      }
      .alignSelf(ItemAlign.Start)
      .margin({ bottom: 16 })

      ForEach(this.analysis!.recommendations, (recommendation: string, index: number) => {
        Row() {
          Text('•')
            .fontSize(16)
            .fontColor('#4CAF50')
            .margin({ right: 8 })

          Text(recommendation)
            .fontSize(14)
            .fontColor('#212121')
            .layoutWeight(1)
        }
        .width('100%')
        .alignItems(VerticalAlign.Top)
        .margin({ bottom: index < this.analysis!.recommendations.length - 1 ? 8 : 0 })
      })
    }
    .width('100%')
    .padding(20)
    .backgroundColor('#E8F5E8')
    .borderRadius(12)
    .margin({ bottom: 20 })
  }

  // 私有方法
  private async loadAnalysis() {
    this.isLoading = true;
    
    try {
      // 加载模拟数据
      this.loadMockAccounts();
      
      // 调用AI分析
      this.analysis = await this.aiService.analyzeSpendingHabits(this.accounts);
      
    } catch (error) {
      console.error('加载分析失败:', error);
    } finally {
      this.isLoading = false;
    }
  }

  private loadMockAccounts() {
    // 模拟账单数据
    this.accounts = [];

    const account1 = new Account();
    account1.amount = 25.50;
    account1.type = AccountType.EXPENSE;
    account1.category = '餐饮';
    this.accounts.push(account1);

    const account2 = new Account();
    account2.amount = 120.00;
    account2.type = AccountType.EXPENSE;
    account2.category = '交通';
    this.accounts.push(account2);
  }

  private getCategoryIcon(category: string): string {
    const iconMap: Record<string, string> = {
      '餐饮': '🍽️',
      '交通': '🚗',
      '购物': '🛍️',
      '娱乐': '🎮',
      '医疗': '🏥',
      '教育': '📚',
      '住房': '🏠'
    };
    return iconMap[category] || '📝';
  }

  private getCategoryColor(category: string): string {
    const colorMap: Record<string, string> = {
      '餐饮': '#FF9800',
      '交通': '#2196F3',
      '购物': '#E91E63',
      '娱乐': '#9C27B0',
      '医疗': '#F44336',
      '教育': '#607D8B',
      '住房': '#795548'
    };
    return colorMap[category] || '#9E9E9E';
  }

  private getCategoryChartData(): ChartData[] {
    return this.analysis!.categoryBreakdown.map(item => ({
      label: item.category,
      value: item.amount,
      color: this.getCategoryColor(item.category),
      percentage: item.percentage
    }));
  }
}
