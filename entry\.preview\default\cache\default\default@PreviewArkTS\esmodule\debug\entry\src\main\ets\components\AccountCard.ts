if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface AccountCard_Params {
    account?: Account;
    showDate?: boolean;
    onItemClick?: (account: Account) => void;
    onItemLongPress?: (account: Account) => void;
}
import { Account } from "@normalized:N&&&entry/src/main/ets/models/Account&";
import { DateUtils } from "@normalized:N&&&entry/src/main/ets/common/utils/DateUtils&";
export class AccountCard extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__account = new SynchedPropertyObjectOneWayPU(params.account, this, "account");
        this.__showDate = new SynchedPropertySimpleOneWayPU(params.showDate, this, "showDate");
        this.onItemClick = undefined;
        this.onItemLongPress = undefined;
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: AccountCard_Params) {
        if (params.account === undefined) {
            this.__account.set(new Account());
        }
        if (params.showDate === undefined) {
            this.__showDate.set(true);
        }
        if (params.onItemClick !== undefined) {
            this.onItemClick = params.onItemClick;
        }
        if (params.onItemLongPress !== undefined) {
            this.onItemLongPress = params.onItemLongPress;
        }
    }
    updateStateVars(params: AccountCard_Params) {
        this.__account.reset(params.account);
        this.__showDate.reset(params.showDate);
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__account.purgeDependencyOnElmtId(rmElmtId);
        this.__showDate.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__account.aboutToBeDeleted();
        this.__showDate.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __account: SynchedPropertySimpleOneWayPU<Account>;
    get account() {
        return this.__account.get();
    }
    set account(newValue: Account) {
        this.__account.set(newValue);
    }
    private __showDate: SynchedPropertySimpleOneWayPU<boolean>;
    get showDate() {
        return this.__showDate.get();
    }
    set showDate(newValue: boolean) {
        this.__showDate.set(newValue);
    }
    private onItemClick?: (account: Account) => void;
    private onItemLongPress?: (account: Account) => void;
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/components/AccountCard.ets(16:5)", "entry");
            Row.width('100%');
            Row.padding(16);
            Row.backgroundColor('#FFFFFF');
            Row.borderRadius(8);
            Row.shadow({
                radius: 4,
                color: '#1F000000',
                offsetX: 0,
                offsetY: 2
            });
            Row.onClick(() => {
                if (this.onItemClick) {
                    this.onItemClick(ObservedObject.GetRawObject(this.account));
                }
            });
            Gesture.create(GesturePriority.Low);
            LongPressGesture.create({ repeat: false });
            LongPressGesture.onAction(() => {
                if (this.onItemLongPress) {
                    this.onItemLongPress(ObservedObject.GetRawObject(this.account));
                }
            });
            LongPressGesture.pop();
            Gesture.pop();
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 分类图标
            Row.create();
            Row.debugLine("entry/src/main/ets/components/AccountCard.ets(18:7)", "entry");
            // 分类图标
            Row.width(48);
            // 分类图标
            Row.height(48);
            // 分类图标
            Row.borderRadius(24);
            // 分类图标
            Row.backgroundColor(this.getCategoryColor());
            // 分类图标
            Row.justifyContent(FlexAlign.Center);
            // 分类图标
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getCategoryIcon());
            Text.debugLine("entry/src/main/ets/components/AccountCard.ets(19:9)", "entry");
            Text.fontSize(24);
            Text.fontColor('#FFFFFF');
        }, Text);
        Text.pop();
        // 分类图标
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 账单信息
            Column.create();
            Column.debugLine("entry/src/main/ets/components/AccountCard.ets(31:7)", "entry");
            // 账单信息
            Column.layoutWeight(1);
            // 账单信息
            Column.margin({ left: 12 });
            // 账单信息
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/components/AccountCard.ets(32:9)", "entry");
            Row.width('100%');
            Row.justifyContent(FlexAlign.SpaceBetween);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.account.category || '未分类');
            Text.debugLine("entry/src/main/ets/components/AccountCard.ets(33:11)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor('#212121');
            Text.maxLines(1);
            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.account.getFormattedAmount());
            Text.debugLine("entry/src/main/ets/components/AccountCard.ets(41:11)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor(this.account.getAmountColor());
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.account.description) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.account.description);
                        Text.debugLine("entry/src/main/ets/components/AccountCard.ets(50:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#757575');
                        Text.maxLines(1);
                        Text.textOverflow({ overflow: TextOverflow.Ellipsis });
                        Text.margin({ top: 4 });
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.showDate) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/components/AccountCard.ets(59:11)", "entry");
                        Row.width('100%');
                        Row.margin({ top: 4 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(DateUtils.formatDate(this.account.date, 'MM-DD HH:mm'));
                        Text.debugLine("entry/src/main/ets/components/AccountCard.ets(60:13)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#9E9E9E');
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.account.isAIGenerated) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create('AI');
                                    Text.debugLine("entry/src/main/ets/components/AccountCard.ets(65:15)", "entry");
                                    Text.fontSize(10);
                                    Text.fontColor('#FFFFFF');
                                    Text.backgroundColor('#2196F3');
                                    Text.padding({ left: 4, right: 4, top: 1, bottom: 1 });
                                    Text.borderRadius(2);
                                    Text.margin({ left: 8 });
                                }, Text);
                                Text.pop();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.account.location) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create(this.account.location);
                                    Text.debugLine("entry/src/main/ets/components/AccountCard.ets(75:15)", "entry");
                                    Text.fontSize(12);
                                    Text.fontColor('#9E9E9E');
                                    Text.maxLines(1);
                                    Text.textOverflow({ overflow: TextOverflow.Ellipsis });
                                    Text.margin({ left: 8 });
                                    Text.layoutWeight(1);
                                }, Text);
                                Text.pop();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    Row.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        // 账单信息
        Column.pop();
        Row.pop();
    }
    private getCategoryIcon(): string {
        // 根据分类返回对应图标，这里简化处理
        const iconMap: Record<string, string> = {
            '餐饮': '🍽️',
            '交通': '🚗',
            '购物': '🛍️',
            '娱乐': '🎮',
            '医疗': '🏥',
            '教育': '📚',
            '住房': '🏠',
            '工资': '💰',
            '奖金': '🎁',
            '投资': '📈',
            '兼职': '💼'
        };
        return iconMap[this.account.category] || '📝';
    }
    private getCategoryColor(): string {
        // 根据分类返回对应颜色，这里简化处理
        const colorMap: Record<string, string> = {
            '餐饮': '#FF9800',
            '交通': '#2196F3',
            '购物': '#E91E63',
            '娱乐': '#9C27B0',
            '医疗': '#F44336',
            '教育': '#607D8B',
            '住房': '#795548',
            '工资': '#4CAF50',
            '奖金': '#8BC34A',
            '投资': '#CDDC39',
            '兼职': '#FFC107'
        };
        return colorMap[this.account.category] || '#9E9E9E';
    }
    rerender() {
        this.updateDirtyElements();
    }
}
