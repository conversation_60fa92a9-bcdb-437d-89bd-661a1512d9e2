/**
 * 格式化工具类
 */
export class FormatUtils {
  
  /**
   * 格式化金额
   */
  static formatAmount(amount: number, showSymbol: boolean = true): string {
    const symbol = showSymbol ? '¥' : '';
    return `${symbol}${amount.toFixed(2)}`;
  }
  
  /**
   * 格式化带符号的金额
   */
  static formatSignedAmount(amount: number, isIncome: boolean): string {
    const sign = isIncome ? '+' : '-';
    const color = isIncome ? '#4CAF50' : '#F44336';
    return `${sign}¥${Math.abs(amount).toFixed(2)}`;
  }
  
  /**
   * 格式化大数字（K, M, B）
   */
  static formatLargeNumber(num: number): string {
    if (num >= 1000000000) {
      return (num / 1000000000).toFixed(1) + 'B';
    }
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }
  
  /**
   * 格式化百分比
   */
  static formatPercentage(value: number, total: number): string {
    if (total === 0) return '0%';
    const percentage = (value / total) * 100;
    return `${percentage.toFixed(1)}%`;
  }
  
  /**
   * 格式化文件大小
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
  
  /**
   * 格式化手机号
   */
  static formatPhoneNumber(phone: string): string {
    if (!phone || phone.length !== 11) return phone;
    return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3');
  }
  
  /**
   * 隐藏手机号中间四位
   */
  static maskPhoneNumber(phone: string): string {
    if (!phone || phone.length !== 11) return phone;
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  }
  
  /**
   * 格式化银行卡号
   */
  static formatBankCard(cardNumber: string): string {
    if (!cardNumber) return cardNumber;
    return cardNumber.replace(/(\d{4})(?=\d)/g, '$1 ');
  }
  
  /**
   * 隐藏银行卡号
   */
  static maskBankCard(cardNumber: string): string {
    if (!cardNumber || cardNumber.length < 8) return cardNumber;
    const start = cardNumber.substring(0, 4);
    const end = cardNumber.substring(cardNumber.length - 4);
    const middle = '*'.repeat(cardNumber.length - 8);
    return `${start}${middle}${end}`;
  }
  
  /**
   * 截断文本
   */
  static truncateText(text: string, maxLength: number): string {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + '...';
  }
  
  /**
   * 首字母大写
   */
  static capitalize(text: string): string {
    if (!text) return text;
    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
  }
  
  /**
   * 驼峰转下划线
   */
  static camelToSnake(text: string): string {
    return text.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }
  
  /**
   * 下划线转驼峰
   */
  static snakeToCamel(text: string): string {
    return text.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
  }
  
  /**
   * 验证邮箱格式
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
  
  /**
   * 验证手机号格式
   */
  static isValidPhoneNumber(phone: string): boolean {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  }
  
  /**
   * 生成随机颜色
   */
  static generateRandomColor(): string {
    const colors = [
      '#F44336', '#E91E63', '#9C27B0', '#673AB7',
      '#3F51B5', '#2196F3', '#03A9F4', '#00BCD4',
      '#009688', '#4CAF50', '#8BC34A', '#CDDC39',
      '#FFEB3B', '#FFC107', '#FF9800', '#FF5722'
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  }
  
  /**
   * 获取对比色（黑色或白色）
   */
  static getContrastColor(hexColor: string): string {
    // 移除#号
    const color = hexColor.replace('#', '');
    
    // 转换为RGB
    const r = parseInt(color.substr(0, 2), 16);
    const g = parseInt(color.substr(2, 2), 16);
    const b = parseInt(color.substr(4, 2), 16);
    
    // 计算亮度
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    
    return brightness > 128 ? '#000000' : '#FFFFFF';
  }
}
