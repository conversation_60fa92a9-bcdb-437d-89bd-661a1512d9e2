{"deviceType": "phone,tablet,2in1,car,wearable,tv", "buildMode": "debug", "note": "false", "logLevel": "3", "isPreview": "true", "port": "29906", "checkEntry": "true", "localPropertiesPath": "E:\\Development\\Project\\Monter\\local.properties", "Path": "E:\\Development\\Tool\\DevEco Studio\\tools\\node\\", "aceProfilePath": "E:\\Development\\Project\\Monter\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", "hapMode": "false", "img2bin": "true", "projectProfilePath": "E:\\Development\\Project\\Monter\\build-profile.json5", "watchMode": "true", "appResource": "E:\\Development\\Project\\Monter\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt", "aceBuildJson": "E:\\Development\\Project\\Monter\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json", "aceModuleRoot": "E:\\Development\\Project\\Monter\\entry\\src\\main\\ets", "aceSoPath": "E:\\Development\\Project\\Monter\\entry\\.preview\\cache\\nativeDependencies.txt", "cachePath": "E:\\Development\\Project\\Monter\\entry\\.preview\\cache\\.default", "aceModuleBuild": "E:\\Development\\Project\\Monter\\entry\\.preview\\default\\intermediates\\assets\\default\\ets", "aceModuleJsonPath": "E:\\Development\\Project\\Monter\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", "stageRouterConfig": {"paths": ["E:\\Development\\Project\\Monter\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", "E:\\Development\\Project\\Monter\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json"], "contents": ["{\"module\":{\"pages\":\"$profile:main_pages\",\"name\":\"entry\"}}", "{\"src\":[\"pages/Index\",\"pages/AddAccountPage\",\"pages/PhotoScanPage\",\"pages/VoiceInputPage\",\"pages/AnalysisPage\",\"pages/DeviceManagePage\",\"pages/SettingsPage\"]}"]}}