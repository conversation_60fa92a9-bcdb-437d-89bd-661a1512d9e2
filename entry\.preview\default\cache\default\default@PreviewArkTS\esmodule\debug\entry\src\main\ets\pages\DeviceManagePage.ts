if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface DeviceManagePage_Params {
    devices?: DeviceInfo[];
    syncStatus?: SyncStatus | null;
    isLoading?: boolean;
    isSyncing?: boolean;
    syncService?: SyncService;
}
import { SyncService } from "@normalized:N&&&entry/src/main/ets/services/SyncService&";
import type { DeviceInfo, SyncStatus } from "@normalized:N&&&entry/src/main/ets/services/SyncService&";
import { DateUtils } from "@normalized:N&&&entry/src/main/ets/common/utils/DateUtils&";
import router from "@ohos:router";
class DeviceManagePage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__devices = new ObservedPropertyObjectPU([], this, "devices");
        this.__syncStatus = new ObservedPropertyObjectPU(null, this, "syncStatus");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__isSyncing = new ObservedPropertySimplePU(false, this, "isSyncing");
        this.syncService = SyncService.getInstance();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: DeviceManagePage_Params) {
        if (params.devices !== undefined) {
            this.devices = params.devices;
        }
        if (params.syncStatus !== undefined) {
            this.syncStatus = params.syncStatus;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.isSyncing !== undefined) {
            this.isSyncing = params.isSyncing;
        }
        if (params.syncService !== undefined) {
            this.syncService = params.syncService;
        }
    }
    updateStateVars(params: DeviceManagePage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__devices.purgeDependencyOnElmtId(rmElmtId);
        this.__syncStatus.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__isSyncing.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__devices.aboutToBeDeleted();
        this.__syncStatus.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__isSyncing.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __devices: ObservedPropertyObjectPU<DeviceInfo[]>;
    get devices() {
        return this.__devices.get();
    }
    set devices(newValue: DeviceInfo[]) {
        this.__devices.set(newValue);
    }
    private __syncStatus: ObservedPropertyObjectPU<SyncStatus | null>;
    get syncStatus() {
        return this.__syncStatus.get();
    }
    set syncStatus(newValue: SyncStatus | null) {
        this.__syncStatus.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __isSyncing: ObservedPropertySimplePU<boolean>;
    get isSyncing() {
        return this.__isSyncing.get();
    }
    set isSyncing(newValue: boolean) {
        this.__isSyncing.set(newValue);
    }
    private syncService: SyncService;
    aboutToAppear() {
        this.loadDevices();
        this.loadSyncStatus();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(21:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        // 顶部导航栏
        this.buildHeader.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 内容区域
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(26:7)", "entry");
            // 内容区域
            Scroll.layoutWeight(1);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(27:9)", "entry");
            Column.padding(16);
        }, Column);
        // 同步状态卡片
        this.buildSyncStatusCard.bind(this)();
        // 设备列表
        this.buildDeviceList.bind(this)();
        // 同步设置
        this.buildSyncSettings.bind(this)();
        Column.pop();
        // 内容区域
        Scroll.pop();
        Column.pop();
    }
    buildHeader(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(48:5)", "entry");
            Row.width('100%');
            Row.padding({ left: 16, right: 16, top: 12, bottom: 12 });
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 返回按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(50:7)", "entry");
            // 返回按钮
            Row.width(40);
            // 返回按钮
            Row.height(40);
            // 返回按钮
            Row.borderRadius(20);
            // 返回按钮
            Row.justifyContent(FlexAlign.Center);
            // 返回按钮
            Row.alignItems(VerticalAlign.Center);
            // 返回按钮
            Row.onClick(() => {
                router.back();
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('←');
            Text.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(51:9)", "entry");
            Text.fontSize(20);
            Text.fontColor('#212121');
        }, Text);
        Text.pop();
        // 返回按钮
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('设备管理');
            Text.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(64:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 刷新按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(72:7)", "entry");
            // 刷新按钮
            Row.width(40);
            // 刷新按钮
            Row.height(40);
            // 刷新按钮
            Row.borderRadius(20);
            // 刷新按钮
            Row.justifyContent(FlexAlign.Center);
            // 刷新按钮
            Row.alignItems(VerticalAlign.Center);
            // 刷新按钮
            Row.onClick(() => {
                this.refreshData();
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🔄');
            Text.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(73:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#212121');
        }, Text);
        Text.pop();
        // 刷新按钮
        Row.pop();
        Row.pop();
    }
    buildSyncStatusCard(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(93:5)", "entry");
            Column.width('100%');
            Column.padding(20);
            Column.backgroundColor('#FFFFFF');
            Column.borderRadius(12);
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(94:7)", "entry");
            Row.width('100%');
            Row.margin({ bottom: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📡');
            Text.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(95:9)", "entry");
            Text.fontSize(20);
            Text.margin({ right: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('同步状态');
            Text.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(99:9)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(104:9)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.syncStatus?.isEnabled) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('已启用');
                        Text.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(107:11)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#FFFFFF');
                        Text.backgroundColor('#4CAF50');
                        Text.padding({ left: 8, right: 8, top: 2, bottom: 2 });
                        Text.borderRadius(10);
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('已禁用');
                        Text.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(114:11)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#FFFFFF');
                        Text.backgroundColor('#F44336');
                        Text.padding({ left: 8, right: 8, top: 2, bottom: 2 });
                        Text.borderRadius(10);
                    }, Text);
                    Text.pop();
                });
            }
        }, If);
        If.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.syncStatus) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(126:9)", "entry");
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(127:11)", "entry");
                        Row.width('100%');
                        Row.margin({ bottom: 8 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('连接设备:');
                        Text.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(128:13)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#757575');
                        Text.width(80);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`${this.syncStatus.connectedDevices}/${this.syncStatus.totalDevices}`);
                        Text.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(133:13)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#212121');
                    }, Text);
                    Text.pop();
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(140:11)", "entry");
                        Row.width('100%');
                        Row.margin({ bottom: 16 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('最后同步:');
                        Text.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(141:13)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#757575');
                        Text.width(80);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.syncStatus.lastSyncTime ?
                            DateUtils.getRelativeTime(this.syncStatus.lastSyncTime) : '从未同步');
                        Text.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(146:13)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#212121');
                    }, Text);
                    Text.pop();
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel(this.isSyncing ? '同步中...' : '立即同步');
                        Button.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(154:11)", "entry");
                        Button.width('100%');
                        Button.height(40);
                        Button.fontSize(14);
                        Button.backgroundColor(this.isSyncing ? '#CCCCCC' : '#1976D2');
                        Button.borderRadius(8);
                        Button.enabled(!this.isSyncing);
                        Button.onClick(() => {
                            this.triggerSync();
                        });
                    }, Button);
                    Button.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    buildDeviceList(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(176:5)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('设备列表');
            Text.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(177:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.devices.length > 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = (_item, index: number) => {
                            const device = _item;
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Row.create();
                                Row.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(186:11)", "entry");
                                Row.width('100%');
                                Row.padding(16);
                                Row.backgroundColor('#FFFFFF');
                                Row.borderRadius(8);
                                Row.margin({ bottom: index < this.devices.length - 1 ? 8 : 0 });
                                Row.onClick(() => {
                                    this.showDeviceDetails(device);
                                });
                            }, Row);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                // 设备图标
                                Text.create(this.getDeviceIcon(device.deviceType));
                                Text.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(188:13)", "entry");
                                // 设备图标
                                Text.fontSize(24);
                                // 设备图标
                                Text.margin({ right: 12 });
                            }, Text);
                            // 设备图标
                            Text.pop();
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                // 设备信息
                                Column.create();
                                Column.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(193:13)", "entry");
                                // 设备信息
                                Column.layoutWeight(1);
                                // 设备信息
                                Column.alignItems(HorizontalAlign.Start);
                            }, Column);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create(device.deviceName);
                                Text.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(194:15)", "entry");
                                Text.fontSize(16);
                                Text.fontWeight(FontWeight.Medium);
                                Text.fontColor('#212121');
                                Text.alignSelf(ItemAlign.Start);
                            }, Text);
                            Text.pop();
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create(`最后同步: ${DateUtils.getRelativeTime(device.lastSyncTime)}`);
                                Text.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(200:15)", "entry");
                                Text.fontSize(12);
                                Text.fontColor('#757575');
                                Text.alignSelf(ItemAlign.Start);
                                Text.margin({ top: 2 });
                            }, Text);
                            Text.pop();
                            // 设备信息
                            Column.pop();
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                // 在线状态
                                Row.create();
                                Row.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(210:13)", "entry");
                            }, Row);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create(device.isOnline ? '●' : '●');
                                Text.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(211:15)", "entry");
                                Text.fontSize(12);
                                Text.fontColor(device.isOnline ? '#4CAF50' : '#9E9E9E');
                                Text.margin({ right: 4 });
                            }, Text);
                            Text.pop();
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create(device.isOnline ? '在线' : '离线');
                                Text.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(216:15)", "entry");
                                Text.fontSize(12);
                                Text.fontColor(device.isOnline ? '#4CAF50' : '#9E9E9E');
                            }, Text);
                            Text.pop();
                            // 在线状态
                            Row.pop();
                            Row.pop();
                        };
                        this.forEachUpdateFunction(elmtId, this.devices, forEachItemGenFunction, undefined, true, false);
                    }, ForEach);
                    ForEach.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(231:9)", "entry");
                        Column.width('100%');
                        Column.padding(40);
                        Column.backgroundColor('#FFFFFF');
                        Column.borderRadius(8);
                        Column.alignItems(HorizontalAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('📱');
                        Text.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(232:11)", "entry");
                        Text.fontSize(48);
                        Text.margin({ bottom: 12 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无设备');
                        Text.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(236:11)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#757575');
                        Text.margin({ bottom: 8 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('请确保其他设备已登录同一账号');
                        Text.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(241:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#9E9E9E');
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    buildSyncSettings(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(258:5)", "entry");
            Column.width('100%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('同步设置');
            Text.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(259:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 自动同步开关
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(267:7)", "entry");
            // 自动同步开关
            Row.width('100%');
            // 自动同步开关
            Row.padding(16);
            // 自动同步开关
            Row.backgroundColor('#FFFFFF');
            // 自动同步开关
            Row.borderRadius(8);
            // 自动同步开关
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(268:9)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('自动同步');
            Text.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(269:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('在设备间自动同步数据');
            Text.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(274:11)", "entry");
            Text.fontSize(12);
            Text.fontColor('#757575');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ top: 2 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Toggle.create({ type: ToggleType.Switch, isOn: true });
            Toggle.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(283:9)", "entry");
            Toggle.onChange((isOn: boolean) => {
                this.syncService.setAutoSync(isOn);
            });
        }, Toggle);
        Toggle.pop();
        // 自动同步开关
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 仅WiFi同步
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(295:7)", "entry");
            // 仅WiFi同步
            Row.width('100%');
            // 仅WiFi同步
            Row.padding(16);
            // 仅WiFi同步
            Row.backgroundColor('#FFFFFF');
            // 仅WiFi同步
            Row.borderRadius(8);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(296:9)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('仅WiFi同步');
            Text.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(297:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('仅在WiFi环境下同步数据');
            Text.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(302:11)", "entry");
            Text.fontSize(12);
            Text.fontColor('#757575');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ top: 2 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Toggle.create({ type: ToggleType.Switch, isOn: true });
            Toggle.debugLine("entry/src/main/ets/pages/DeviceManagePage.ets(311:9)", "entry");
            Toggle.onChange((isOn: boolean) => {
                console.log('仅WiFi同步:', isOn);
            });
        }, Toggle);
        Toggle.pop();
        // 仅WiFi同步
        Row.pop();
        Column.pop();
    }
    // 私有方法
    private async loadDevices() {
        this.isLoading = true;
        try {
            this.devices = await this.syncService.getAvailableDevices();
        }
        catch (error) {
            console.error('加载设备列表失败:', error);
        }
        finally {
            this.isLoading = false;
        }
    }
    private async loadSyncStatus() {
        try {
            this.syncStatus = await this.syncService.getSyncStatus();
        }
        catch (error) {
            console.error('加载同步状态失败:', error);
        }
    }
    private async refreshData() {
        await Promise.all([
            this.loadDevices(),
            this.loadSyncStatus()
        ]);
    }
    private async triggerSync() {
        this.isSyncing = true;
        try {
            const success = await this.syncService.triggerSync();
            if (success) {
                console.log('同步成功');
                await this.loadSyncStatus();
            }
            else {
                console.log('同步失败');
            }
        }
        catch (error) {
            console.error('同步失败:', error);
        }
        finally {
            this.isSyncing = false;
        }
    }
    private showDeviceDetails(device: DeviceInfo) {
        console.log('显示设备详情:', device);
        // TODO: 显示设备详情对话框
    }
    private getDeviceIcon(deviceType: string): string {
        const iconMap: Record<string, string> = {
            'phone': '📱',
            'tablet': '📱',
            'wearable': '⌚',
            'tv': '📺',
            'car': '🚗'
        };
        return iconMap[deviceType] || '📱';
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "DeviceManagePage";
    }
}
registerNamedRoute(() => new DeviceManagePage(undefined, {}), "", { bundleName: "com.silence.monter", moduleName: "entry", pagePath: "pages/DeviceManagePage", pageFullPath: "entry/src/main/ets/pages/DeviceManagePage", integratedHsp: "false", moduleType: "followWithHap" });
