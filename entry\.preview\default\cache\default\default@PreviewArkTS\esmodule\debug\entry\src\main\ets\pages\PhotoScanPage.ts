if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface PhotoScanPage_Params {
    isScanning?: boolean;
    scanResult?: ReceiptInfo | null;
    showResult?: boolean;
    errorMessage?: string;
    capturedImage?: string;
    hasPermission?: boolean;
    showPermissionDialog?: boolean;
    aiService?: AIService;
    permissionService?: PermissionService;
}
import { Account, AccountType } from "@normalized:N&&&entry/src/main/ets/models/Account&";
import { AIService } from "@normalized:N&&&entry/src/main/ets/services/AIService&";
import type { ReceiptInfo, ReceiptItem } from "@normalized:N&&&entry/src/main/ets/services/AIService&";
import { PermissionService, PermissionStatus } from "@normalized:N&&&entry/src/main/ets/services/PermissionService&";
import router from "@ohos:router";
class PhotoScanPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__isScanning = new ObservedPropertySimplePU(false, this, "isScanning");
        this.__scanResult = new ObservedPropertyObjectPU(null, this, "scanResult");
        this.__showResult = new ObservedPropertySimplePU(false, this, "showResult");
        this.__errorMessage = new ObservedPropertySimplePU('', this, "errorMessage");
        this.__capturedImage = new ObservedPropertySimplePU('', this, "capturedImage");
        this.__hasPermission = new ObservedPropertySimplePU(false, this, "hasPermission");
        this.__showPermissionDialog = new ObservedPropertySimplePU(false, this, "showPermissionDialog");
        this.aiService = AIService.getInstance();
        this.permissionService = PermissionService.getInstance();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: PhotoScanPage_Params) {
        if (params.isScanning !== undefined) {
            this.isScanning = params.isScanning;
        }
        if (params.scanResult !== undefined) {
            this.scanResult = params.scanResult;
        }
        if (params.showResult !== undefined) {
            this.showResult = params.showResult;
        }
        if (params.errorMessage !== undefined) {
            this.errorMessage = params.errorMessage;
        }
        if (params.capturedImage !== undefined) {
            this.capturedImage = params.capturedImage;
        }
        if (params.hasPermission !== undefined) {
            this.hasPermission = params.hasPermission;
        }
        if (params.showPermissionDialog !== undefined) {
            this.showPermissionDialog = params.showPermissionDialog;
        }
        if (params.aiService !== undefined) {
            this.aiService = params.aiService;
        }
        if (params.permissionService !== undefined) {
            this.permissionService = params.permissionService;
        }
    }
    updateStateVars(params: PhotoScanPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__isScanning.purgeDependencyOnElmtId(rmElmtId);
        this.__scanResult.purgeDependencyOnElmtId(rmElmtId);
        this.__showResult.purgeDependencyOnElmtId(rmElmtId);
        this.__errorMessage.purgeDependencyOnElmtId(rmElmtId);
        this.__capturedImage.purgeDependencyOnElmtId(rmElmtId);
        this.__hasPermission.purgeDependencyOnElmtId(rmElmtId);
        this.__showPermissionDialog.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__isScanning.aboutToBeDeleted();
        this.__scanResult.aboutToBeDeleted();
        this.__showResult.aboutToBeDeleted();
        this.__errorMessage.aboutToBeDeleted();
        this.__capturedImage.aboutToBeDeleted();
        this.__hasPermission.aboutToBeDeleted();
        this.__showPermissionDialog.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __isScanning: ObservedPropertySimplePU<boolean>;
    get isScanning() {
        return this.__isScanning.get();
    }
    set isScanning(newValue: boolean) {
        this.__isScanning.set(newValue);
    }
    private __scanResult: ObservedPropertyObjectPU<ReceiptInfo | null>;
    get scanResult() {
        return this.__scanResult.get();
    }
    set scanResult(newValue: ReceiptInfo | null) {
        this.__scanResult.set(newValue);
    }
    private __showResult: ObservedPropertySimplePU<boolean>;
    get showResult() {
        return this.__showResult.get();
    }
    set showResult(newValue: boolean) {
        this.__showResult.set(newValue);
    }
    private __errorMessage: ObservedPropertySimplePU<string>;
    get errorMessage() {
        return this.__errorMessage.get();
    }
    set errorMessage(newValue: string) {
        this.__errorMessage.set(newValue);
    }
    private __capturedImage: ObservedPropertySimplePU<string>;
    get capturedImage() {
        return this.__capturedImage.get();
    }
    set capturedImage(newValue: string) {
        this.__capturedImage.set(newValue);
    }
    private __hasPermission: ObservedPropertySimplePU<boolean>;
    get hasPermission() {
        return this.__hasPermission.get();
    }
    set hasPermission(newValue: boolean) {
        this.__hasPermission.set(newValue);
    }
    private __showPermissionDialog: ObservedPropertySimplePU<boolean>;
    get showPermissionDialog() {
        return this.__showPermissionDialog.get();
    }
    set showPermissionDialog(newValue: boolean) {
        this.__showPermissionDialog.set(newValue);
    }
    private aiService: AIService;
    private permissionService: PermissionService;
    aboutToAppear() {
        this.checkCameraPermission();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(26:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#000000');
        }, Column);
        // 顶部导航栏
        this.buildHeader.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (!this.hasPermission) {
                this.ifElseBranchUpdateFunction(0, () => {
                    // 权限请求界面
                    this.buildPermissionView.bind(this)();
                });
            }
            else if (!this.showResult) {
                this.ifElseBranchUpdateFunction(1, () => {
                    // 拍照界面
                    this.buildCameraView.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    // 识别结果界面
                    this.buildResultView.bind(this)();
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 权限说明对话框
            if (this.showPermissionDialog) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.buildPermissionDialog.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    buildHeader(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(53:5)", "entry");
            Row.width('100%');
            Row.padding({ left: 16, right: 16, top: 12, bottom: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 返回按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(55:7)", "entry");
            // 返回按钮
            Row.width(40);
            // 返回按钮
            Row.height(40);
            // 返回按钮
            Row.borderRadius(20);
            // 返回按钮
            Row.justifyContent(FlexAlign.Center);
            // 返回按钮
            Row.alignItems(VerticalAlign.Center);
            // 返回按钮
            Row.onClick(() => {
                router.back();
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('←');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(56:9)", "entry");
            Text.fontSize(20);
            Text.fontColor('#FFFFFF');
        }, Text);
        Text.pop();
        // 返回按钮
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('拍照识别');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(69:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#FFFFFF');
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 帮助按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(77:7)", "entry");
            // 帮助按钮
            Row.width(40);
            // 帮助按钮
            Row.height(40);
            // 帮助按钮
            Row.borderRadius(20);
            // 帮助按钮
            Row.justifyContent(FlexAlign.Center);
            // 帮助按钮
            Row.alignItems(VerticalAlign.Center);
            // 帮助按钮
            Row.onClick(() => {
                this.showHelpDialog();
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('?');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(78:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#FFFFFF');
        }, Text);
        Text.pop();
        // 帮助按钮
        Row.pop();
        Row.pop();
    }
    buildPermissionView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(97:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
            Column.padding(40);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📷');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(98:7)", "entry");
            Text.fontSize(80);
            Text.fontColor('#FFFFFF');
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('需要相机权限');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(103:7)", "entry");
            Text.fontSize(20);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#FFFFFF');
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('拍照识别功能需要使用相机权限来拍摄小票');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(109:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#CCCCCC');
            Text.textAlign(TextAlign.Center);
            Text.margin({ bottom: 40 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('授权相机权限');
            Button.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(115:7)", "entry");
            Button.width(200);
            Button.height(48);
            Button.fontSize(16);
            Button.backgroundColor('#1976D2');
            Button.borderRadius(24);
            Button.onClick(() => {
                this.requestCameraPermission();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('了解更多');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(125:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#1976D2');
            Text.margin({ top: 20 });
            Text.onClick(() => {
                this.showPermissionDialog = true;
            });
        }, Text);
        Text.pop();
        Column.pop();
    }
    buildPermissionDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(142:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#80000000');
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
            Column.onClick(() => {
                this.showPermissionDialog = false;
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(143:7)", "entry");
            Column.width('85%');
            Column.padding(24);
            Column.backgroundColor('#FFFFFF');
            Column.borderRadius(12);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('相机权限说明');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(144:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('MonterAI需要相机权限来实现以下功能：');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(150:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#757575');
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(155:9)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('• 拍摄小票和发票');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(156:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('• AI识别消费信息');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(162:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('• 自动记录账单数据');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(168:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('我们承诺不会在未经您同意的情况下使用相机功能');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(176:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#9E9E9E');
            Text.textAlign(TextAlign.Center);
            Text.margin({ bottom: 24 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(182:9)", "entry");
            Row.width('100%');
            Row.justifyContent(FlexAlign.SpaceBetween);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(183:11)", "entry");
            Button.width('45%');
            Button.height(40);
            Button.fontSize(14);
            Button.backgroundColor('#E0E0E0');
            Button.fontColor('#212121');
            Button.borderRadius(8);
            Button.onClick(() => {
                this.showPermissionDialog = false;
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('授权');
            Button.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(194:11)", "entry");
            Button.width('45%');
            Button.height(40);
            Button.fontSize(14);
            Button.backgroundColor('#1976D2');
            Button.borderRadius(8);
            Button.onClick(() => {
                this.showPermissionDialog = false;
                this.requestCameraPermission();
            });
        }, Button);
        Button.pop();
        Row.pop();
        Column.pop();
        Column.pop();
    }
    buildCameraView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(225:5)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 相机预览区域
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(227:7)", "entry");
            // 相机预览区域
            Stack.layoutWeight(1);
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 这里应该是相机预览组件，暂时用占位符
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(229:9)", "entry");
            // 这里应该是相机预览组件，暂时用占位符
            Column.width('100%');
            // 这里应该是相机预览组件，暂时用占位符
            Column.height('100%');
            // 这里应该是相机预览组件，暂时用占位符
            Column.justifyContent(FlexAlign.Center);
            // 这里应该是相机预览组件，暂时用占位符
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📷');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(230:11)", "entry");
            Text.fontSize(80);
            Text.fontColor('#FFFFFF');
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('将小票对准取景框');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(235:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#FFFFFF');
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        // 这里应该是相机预览组件，暂时用占位符
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 取景框
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(246:9)", "entry");
            // 取景框
            Column.justifyContent(FlexAlign.Center);
            // 取景框
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(247:11)", "entry");
            Row.width(200);
            Row.height(2);
            Row.backgroundColor('#00FF00');
        }, Row);
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(252:11)", "entry");
            Row.width(2);
            Row.height(300);
            Row.backgroundColor('#00FF00');
            Row.margin({ top: -2 });
        }, Row);
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(258:11)", "entry");
            Row.width(200);
            Row.height(2);
            Row.backgroundColor('#00FF00');
            Row.margin({ top: -2 });
        }, Row);
        Row.pop();
        // 取景框
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 扫描状态提示
            if (this.isScanning) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(269:11)", "entry");
                        Column.width('100%');
                        Column.height('100%');
                        Column.justifyContent(FlexAlign.Center);
                        Column.alignItems(HorizontalAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('正在识别中...');
                        Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(270:13)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#FFFFFF');
                        Text.backgroundColor('#80000000');
                        Text.padding(12);
                        Text.borderRadius(8);
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        // 相机预览区域
        Stack.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 底部操作栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(286:7)", "entry");
            // 底部操作栏
            Row.width('100%');
            // 底部操作栏
            Row.padding({ left: 20, right: 20, top: 20, bottom: 40 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 相册按钮
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(288:9)", "entry");
            // 相册按钮
            Column.width(60);
            // 相册按钮
            Column.alignItems(HorizontalAlign.Center);
            // 相册按钮
            Column.onClick(() => {
                this.selectFromGallery();
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🖼️');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(289:11)", "entry");
            Text.fontSize(24);
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('相册');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(293:11)", "entry");
            Text.fontSize(12);
            Text.fontColor('#FFFFFF');
        }, Text);
        Text.pop();
        // 相册按钮
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(303:9)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 拍照按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(306:9)", "entry");
            // 拍照按钮
            Row.width(80);
            // 拍照按钮
            Row.height(80);
            // 拍照按钮
            Row.borderRadius(40);
            // 拍照按钮
            Row.backgroundColor('#1976D2');
            // 拍照按钮
            Row.justifyContent(FlexAlign.Center);
            // 拍照按钮
            Row.alignItems(VerticalAlign.Center);
            // 拍照按钮
            Row.enabled(!this.isScanning);
            // 拍照按钮
            Row.onClick(() => {
                this.capturePhoto();
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📸');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(307:11)", "entry");
            Text.fontSize(32);
            Text.fontColor('#FFFFFF');
        }, Text);
        Text.pop();
        // 拍照按钮
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(322:9)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 闪光灯按钮
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(325:9)", "entry");
            // 闪光灯按钮
            Column.width(60);
            // 闪光灯按钮
            Column.alignItems(HorizontalAlign.Center);
            // 闪光灯按钮
            Column.onClick(() => {
                this.toggleFlash();
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('⚡');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(326:11)", "entry");
            Text.fontSize(24);
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('闪光灯');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(330:11)", "entry");
            Text.fontSize(12);
            Text.fontColor('#FFFFFF');
        }, Text);
        Text.pop();
        // 闪光灯按钮
        Column.pop();
        // 底部操作栏
        Row.pop();
        Column.pop();
    }
    buildResultView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(347:5)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 识别结果
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(349:7)", "entry");
            // 识别结果
            Scroll.layoutWeight(1);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(350:9)", "entry");
            Column.width('100%');
            Column.padding(20);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.scanResult) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 商家信息
                        Text.create(this.scanResult.merchantName);
                        Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(353:13)", "entry");
                        // 商家信息
                        Text.fontSize(20);
                        // 商家信息
                        Text.fontWeight(FontWeight.Bold);
                        // 商家信息
                        Text.fontColor('#FFFFFF');
                        // 商家信息
                        Text.margin({ bottom: 8 });
                    }, Text);
                    // 商家信息
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 总金额
                        Text.create(`¥${this.scanResult.totalAmount.toFixed(2)}`);
                        Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(360:13)", "entry");
                        // 总金额
                        Text.fontSize(32);
                        // 总金额
                        Text.fontWeight(FontWeight.Bold);
                        // 总金额
                        Text.fontColor('#4CAF50');
                        // 总金额
                        Text.margin({ bottom: 16 });
                    }, Text);
                    // 总金额
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 置信度
                        Text.create(`识别准确度: ${(this.scanResult.confidence * 100).toFixed(0)}%`);
                        Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(367:13)", "entry");
                        // 置信度
                        Text.fontSize(14);
                        // 置信度
                        Text.fontColor('#CCCCCC');
                        // 置信度
                        Text.margin({ bottom: 20 });
                    }, Text);
                    // 置信度
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        // 商品列表
                        if (this.scanResult.items.length > 0) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create('商品明细');
                                    Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(374:15)", "entry");
                                    Text.fontSize(16);
                                    Text.fontWeight(FontWeight.Bold);
                                    Text.fontColor('#FFFFFF');
                                    Text.alignSelf(ItemAlign.Start);
                                    Text.margin({ bottom: 12 });
                                }, Text);
                                Text.pop();
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    ForEach.create();
                                    const forEachItemGenFunction = (_item, index: number) => {
                                        const item = _item;
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Row.create();
                                            Row.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(382:17)", "entry");
                                            Row.width('100%');
                                            Row.padding({ top: 8, bottom: 8 });
                                            Row.margin({ bottom: index < this.scanResult!.items.length - 1 ? 4 : 0 });
                                        }, Row);
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Text.create(item.name);
                                            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(383:19)", "entry");
                                            Text.fontSize(14);
                                            Text.fontColor('#FFFFFF');
                                            Text.layoutWeight(1);
                                        }, Text);
                                        Text.pop();
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Text.create(`${item.quantity}x`);
                                            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(388:19)", "entry");
                                            Text.fontSize(14);
                                            Text.fontColor('#CCCCCC');
                                            Text.margin({ right: 8 });
                                        }, Text);
                                        Text.pop();
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Text.create(`¥${item.price.toFixed(2)}`);
                                            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(393:19)", "entry");
                                            Text.fontSize(14);
                                            Text.fontColor('#FFFFFF');
                                        }, Text);
                                        Text.pop();
                                        Row.pop();
                                    };
                                    this.forEachUpdateFunction(elmtId, this.scanResult.items, forEachItemGenFunction, undefined, true, false);
                                }, ForEach);
                                ForEach.pop();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.errorMessage) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.errorMessage);
                        Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(405:13)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#F44336');
                        Text.textAlign(TextAlign.Center);
                        Text.margin({ top: 20 });
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
        // 识别结果
        Scroll.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 底部按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(418:7)", "entry");
            // 底部按钮
            Row.width('100%');
            // 底部按钮
            Row.padding(20);
            // 底部按钮
            Row.justifyContent(FlexAlign.SpaceBetween);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('重新拍照');
            Button.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(419:9)", "entry");
            Button.width('45%');
            Button.height(48);
            Button.fontSize(16);
            Button.backgroundColor('#424242');
            Button.borderRadius(8);
            Button.onClick(() => {
                this.resetScan();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('确认记账');
            Button.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(429:9)", "entry");
            Button.width('45%');
            Button.height(48);
            Button.fontSize(16);
            Button.backgroundColor('#1976D2');
            Button.borderRadius(8);
            Button.enabled(this.scanResult !== null);
            Button.onClick(() => {
                this.confirmAndSave();
            });
        }, Button);
        Button.pop();
        // 底部按钮
        Row.pop();
        Column.pop();
    }
    // 权限相关方法
    private async checkCameraPermission(): Promise<void> {
        try {
            const status = await this.permissionService.checkCameraPermission();
            this.hasPermission = status === PermissionStatus.GRANTED;
            if (!this.hasPermission) {
                console.log('相机权限未授权');
            }
        }
        catch (error) {
            console.error('检查相机权限失败:', error);
            this.hasPermission = false;
        }
    }
    private async requestCameraPermission(): Promise<void> {
        try {
            const result = await this.permissionService.requestCameraPermission();
            if (result.status === PermissionStatus.GRANTED) {
                this.hasPermission = true;
                console.log('相机权限授权成功');
            }
            else {
                this.hasPermission = false;
                console.log('相机权限被拒绝:', result.message);
                // 可以显示引导用户到设置页面的提示
                if (result.status === PermissionStatus.DENIED) {
                    this.showPermissionDeniedTip();
                }
            }
        }
        catch (error) {
            console.error('请求相机权限失败:', error);
            this.hasPermission = false;
        }
    }
    private showPermissionDeniedTip(): void {
        console.log('显示权限被拒绝提示');
        // TODO: 显示引导用户到设置页面开启权限的提示
    }
    // 事件处理方法
    private async capturePhoto() {
        try {
            this.isScanning = true;
            this.errorMessage = '';
            // 模拟拍照过程
            await this.delay(500);
            this.capturedImage = 'mock_image_path.jpg';
            // 调用AI识别
            this.scanResult = await this.aiService.recognizeReceipt(this.capturedImage);
            this.showResult = true;
        }
        catch (error) {
            this.errorMessage = '识别失败，请重试';
        }
        finally {
            this.isScanning = false;
        }
    }
    private selectFromGallery() {
        console.log('从相册选择');
        // TODO: 实现从相册选择图片
    }
    private toggleFlash() {
        console.log('切换闪光灯');
        // TODO: 实现闪光灯切换
    }
    private showHelpDialog() {
        console.log('显示帮助信息');
        // TODO: 显示拍照识别帮助信息
    }
    private resetScan() {
        this.showResult = false;
        this.scanResult = null;
        this.errorMessage = '';
        this.capturedImage = '';
    }
    private confirmAndSave() {
        if (!this.scanResult)
            return;
        // 创建账单对象
        const account = new Account();
        account.amount = this.scanResult.totalAmount;
        account.type = AccountType.EXPENSE;
        account.category = this.scanResult.category;
        account.description = this.scanResult.merchantName;
        account.date = this.scanResult.date;
        account.isAIGenerated = true;
        account.aiConfidence = this.scanResult.confidence;
        console.log('保存AI识别账单:', account);
        // TODO: 保存到数据库
        // 返回主页
        router.back();
    }
    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "PhotoScanPage";
    }
}
registerNamedRoute(() => new PhotoScanPage(undefined, {}), "", { bundleName: "com.silence.monter", moduleName: "entry", pagePath: "pages/PhotoScanPage", pageFullPath: "entry/src/main/ets/pages/PhotoScanPage", integratedHsp: "false", moduleType: "followWithHap" });
