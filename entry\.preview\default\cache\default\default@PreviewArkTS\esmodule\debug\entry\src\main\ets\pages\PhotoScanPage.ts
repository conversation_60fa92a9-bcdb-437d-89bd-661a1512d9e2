if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface PhotoScanPage_Params {
    isScanning?: boolean;
    scanResult?: ReceiptInfo | null;
    showResult?: boolean;
    errorMessage?: string;
    capturedImage?: string;
    aiService?: AIService;
}
import { Account, AccountType } from "@normalized:N&&&entry/src/main/ets/models/Account&";
import { AIService } from "@normalized:N&&&entry/src/main/ets/services/AIService&";
import type { ReceiptInfo } from "@normalized:N&&&entry/src/main/ets/services/AIService&";
import router from "@ohos:router";
class PhotoScanPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__isScanning = new ObservedPropertySimplePU(false, this, "isScanning");
        this.__scanResult = new ObservedPropertyObjectPU(null, this, "scanResult");
        this.__showResult = new ObservedPropertySimplePU(false, this, "showResult");
        this.__errorMessage = new ObservedPropertySimplePU('', this, "errorMessage");
        this.__capturedImage = new ObservedPropertySimplePU('', this, "capturedImage");
        this.aiService = AIService.getInstance();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: PhotoScanPage_Params) {
        if (params.isScanning !== undefined) {
            this.isScanning = params.isScanning;
        }
        if (params.scanResult !== undefined) {
            this.scanResult = params.scanResult;
        }
        if (params.showResult !== undefined) {
            this.showResult = params.showResult;
        }
        if (params.errorMessage !== undefined) {
            this.errorMessage = params.errorMessage;
        }
        if (params.capturedImage !== undefined) {
            this.capturedImage = params.capturedImage;
        }
        if (params.aiService !== undefined) {
            this.aiService = params.aiService;
        }
    }
    updateStateVars(params: PhotoScanPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__isScanning.purgeDependencyOnElmtId(rmElmtId);
        this.__scanResult.purgeDependencyOnElmtId(rmElmtId);
        this.__showResult.purgeDependencyOnElmtId(rmElmtId);
        this.__errorMessage.purgeDependencyOnElmtId(rmElmtId);
        this.__capturedImage.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__isScanning.aboutToBeDeleted();
        this.__scanResult.aboutToBeDeleted();
        this.__showResult.aboutToBeDeleted();
        this.__errorMessage.aboutToBeDeleted();
        this.__capturedImage.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __isScanning: ObservedPropertySimplePU<boolean>;
    get isScanning() {
        return this.__isScanning.get();
    }
    set isScanning(newValue: boolean) {
        this.__isScanning.set(newValue);
    }
    private __scanResult: ObservedPropertyObjectPU<ReceiptInfo | null>;
    get scanResult() {
        return this.__scanResult.get();
    }
    set scanResult(newValue: ReceiptInfo | null) {
        this.__scanResult.set(newValue);
    }
    private __showResult: ObservedPropertySimplePU<boolean>;
    get showResult() {
        return this.__showResult.get();
    }
    set showResult(newValue: boolean) {
        this.__showResult.set(newValue);
    }
    private __errorMessage: ObservedPropertySimplePU<string>;
    get errorMessage() {
        return this.__errorMessage.get();
    }
    set errorMessage(newValue: string) {
        this.__errorMessage.set(newValue);
    }
    private __capturedImage: ObservedPropertySimplePU<string>;
    get capturedImage() {
        return this.__capturedImage.get();
    }
    set capturedImage(newValue: string) {
        this.__capturedImage.set(newValue);
    }
    private aiService: AIService;
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(18:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#000000');
        }, Column);
        // 顶部导航栏
        this.buildHeader.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (!this.showResult) {
                this.ifElseBranchUpdateFunction(0, () => {
                    // 拍照界面
                    this.buildCameraView.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    // 识别结果界面
                    this.buildResultView.bind(this)();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    buildHeader(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(37:5)", "entry");
            Row.width('100%');
            Row.padding({ left: 16, right: 16, top: 12, bottom: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 返回按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(39:7)", "entry");
            // 返回按钮
            Row.width(40);
            // 返回按钮
            Row.height(40);
            // 返回按钮
            Row.borderRadius(20);
            // 返回按钮
            Row.justifyContent(FlexAlign.Center);
            // 返回按钮
            Row.alignItems(VerticalAlign.Center);
            // 返回按钮
            Row.onClick(() => {
                router.back();
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('←');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(40:9)", "entry");
            Text.fontSize(20);
            Text.fontColor('#FFFFFF');
        }, Text);
        Text.pop();
        // 返回按钮
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('拍照识别');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(53:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#FFFFFF');
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 帮助按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(61:7)", "entry");
            // 帮助按钮
            Row.width(40);
            // 帮助按钮
            Row.height(40);
            // 帮助按钮
            Row.borderRadius(20);
            // 帮助按钮
            Row.justifyContent(FlexAlign.Center);
            // 帮助按钮
            Row.alignItems(VerticalAlign.Center);
            // 帮助按钮
            Row.onClick(() => {
                this.showHelpDialog();
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('?');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(62:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#FFFFFF');
        }, Text);
        Text.pop();
        // 帮助按钮
        Row.pop();
        Row.pop();
    }
    buildCameraView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(81:5)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 相机预览区域
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(83:7)", "entry");
            // 相机预览区域
            Stack.layoutWeight(1);
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 这里应该是相机预览组件，暂时用占位符
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(85:9)", "entry");
            // 这里应该是相机预览组件，暂时用占位符
            Column.width('100%');
            // 这里应该是相机预览组件，暂时用占位符
            Column.height('100%');
            // 这里应该是相机预览组件，暂时用占位符
            Column.justifyContent(FlexAlign.Center);
            // 这里应该是相机预览组件，暂时用占位符
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📷');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(86:11)", "entry");
            Text.fontSize(80);
            Text.fontColor('#FFFFFF');
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('将小票对准取景框');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(91:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#FFFFFF');
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        // 这里应该是相机预览组件，暂时用占位符
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 取景框
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(102:9)", "entry");
            // 取景框
            Column.justifyContent(FlexAlign.Center);
            // 取景框
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(103:11)", "entry");
            Row.width(200);
            Row.height(2);
            Row.backgroundColor('#00FF00');
        }, Row);
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(108:11)", "entry");
            Row.width(2);
            Row.height(300);
            Row.backgroundColor('#00FF00');
            Row.margin({ top: -2 });
        }, Row);
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(114:11)", "entry");
            Row.width(200);
            Row.height(2);
            Row.backgroundColor('#00FF00');
            Row.margin({ top: -2 });
        }, Row);
        Row.pop();
        // 取景框
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 扫描状态提示
            if (this.isScanning) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(125:11)", "entry");
                        Column.width('100%');
                        Column.height('100%');
                        Column.justifyContent(FlexAlign.Center);
                        Column.alignItems(HorizontalAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('正在识别中...');
                        Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(126:13)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#FFFFFF');
                        Text.backgroundColor('#80000000');
                        Text.padding(12);
                        Text.borderRadius(8);
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        // 相机预览区域
        Stack.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 底部操作栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(142:7)", "entry");
            // 底部操作栏
            Row.width('100%');
            // 底部操作栏
            Row.padding({ left: 20, right: 20, top: 20, bottom: 40 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 相册按钮
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(144:9)", "entry");
            // 相册按钮
            Column.width(60);
            // 相册按钮
            Column.alignItems(HorizontalAlign.Center);
            // 相册按钮
            Column.onClick(() => {
                this.selectFromGallery();
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🖼️');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(145:11)", "entry");
            Text.fontSize(24);
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('相册');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(149:11)", "entry");
            Text.fontSize(12);
            Text.fontColor('#FFFFFF');
        }, Text);
        Text.pop();
        // 相册按钮
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(159:9)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 拍照按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(162:9)", "entry");
            // 拍照按钮
            Row.width(80);
            // 拍照按钮
            Row.height(80);
            // 拍照按钮
            Row.borderRadius(40);
            // 拍照按钮
            Row.backgroundColor('#1976D2');
            // 拍照按钮
            Row.justifyContent(FlexAlign.Center);
            // 拍照按钮
            Row.alignItems(VerticalAlign.Center);
            // 拍照按钮
            Row.enabled(!this.isScanning);
            // 拍照按钮
            Row.onClick(() => {
                this.capturePhoto();
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📸');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(163:11)", "entry");
            Text.fontSize(32);
            Text.fontColor('#FFFFFF');
        }, Text);
        Text.pop();
        // 拍照按钮
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(178:9)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 闪光灯按钮
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(181:9)", "entry");
            // 闪光灯按钮
            Column.width(60);
            // 闪光灯按钮
            Column.alignItems(HorizontalAlign.Center);
            // 闪光灯按钮
            Column.onClick(() => {
                this.toggleFlash();
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('⚡');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(182:11)", "entry");
            Text.fontSize(24);
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('闪光灯');
            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(186:11)", "entry");
            Text.fontSize(12);
            Text.fontColor('#FFFFFF');
        }, Text);
        Text.pop();
        // 闪光灯按钮
        Column.pop();
        // 底部操作栏
        Row.pop();
        Column.pop();
    }
    buildResultView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(203:5)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 识别结果
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(205:7)", "entry");
            // 识别结果
            Scroll.layoutWeight(1);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(206:9)", "entry");
            Column.width('100%');
            Column.padding(20);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.scanResult) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 商家信息
                        Text.create(this.scanResult.merchantName);
                        Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(209:13)", "entry");
                        // 商家信息
                        Text.fontSize(20);
                        // 商家信息
                        Text.fontWeight(FontWeight.Bold);
                        // 商家信息
                        Text.fontColor('#FFFFFF');
                        // 商家信息
                        Text.margin({ bottom: 8 });
                    }, Text);
                    // 商家信息
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 总金额
                        Text.create(`¥${this.scanResult.totalAmount.toFixed(2)}`);
                        Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(216:13)", "entry");
                        // 总金额
                        Text.fontSize(32);
                        // 总金额
                        Text.fontWeight(FontWeight.Bold);
                        // 总金额
                        Text.fontColor('#4CAF50');
                        // 总金额
                        Text.margin({ bottom: 16 });
                    }, Text);
                    // 总金额
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 置信度
                        Text.create(`识别准确度: ${(this.scanResult.confidence * 100).toFixed(0)}%`);
                        Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(223:13)", "entry");
                        // 置信度
                        Text.fontSize(14);
                        // 置信度
                        Text.fontColor('#CCCCCC');
                        // 置信度
                        Text.margin({ bottom: 20 });
                    }, Text);
                    // 置信度
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        // 商品列表
                        if (this.scanResult.items.length > 0) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create('商品明细');
                                    Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(230:15)", "entry");
                                    Text.fontSize(16);
                                    Text.fontWeight(FontWeight.Bold);
                                    Text.fontColor('#FFFFFF');
                                    Text.alignSelf(ItemAlign.Start);
                                    Text.margin({ bottom: 12 });
                                }, Text);
                                Text.pop();
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    ForEach.create();
                                    const forEachItemGenFunction = (_item, index) => {
                                        const item = _item;
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Row.create();
                                            Row.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(238:17)", "entry");
                                            Row.width('100%');
                                            Row.padding({ top: 8, bottom: 8 });
                                            Row.margin({ bottom: index < this.scanResult!.items.length - 1 ? 4 : 0 });
                                        }, Row);
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Text.create(item.name);
                                            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(239:19)", "entry");
                                            Text.fontSize(14);
                                            Text.fontColor('#FFFFFF');
                                            Text.layoutWeight(1);
                                        }, Text);
                                        Text.pop();
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Text.create(`${item.quantity}x`);
                                            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(244:19)", "entry");
                                            Text.fontSize(14);
                                            Text.fontColor('#CCCCCC');
                                            Text.margin({ right: 8 });
                                        }, Text);
                                        Text.pop();
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Text.create(`¥${item.price.toFixed(2)}`);
                                            Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(249:19)", "entry");
                                            Text.fontSize(14);
                                            Text.fontColor('#FFFFFF');
                                        }, Text);
                                        Text.pop();
                                        Row.pop();
                                    };
                                    this.forEachUpdateFunction(elmtId, this.scanResult.items, forEachItemGenFunction, undefined, true, false);
                                }, ForEach);
                                ForEach.pop();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.errorMessage) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.errorMessage);
                        Text.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(261:13)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#F44336');
                        Text.textAlign(TextAlign.Center);
                        Text.margin({ top: 20 });
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
        // 识别结果
        Scroll.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 底部按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(274:7)", "entry");
            // 底部按钮
            Row.width('100%');
            // 底部按钮
            Row.padding(20);
            // 底部按钮
            Row.justifyContent(FlexAlign.SpaceBetween);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('重新拍照');
            Button.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(275:9)", "entry");
            Button.width('45%');
            Button.height(48);
            Button.fontSize(16);
            Button.backgroundColor('#424242');
            Button.borderRadius(8);
            Button.onClick(() => {
                this.resetScan();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('确认记账');
            Button.debugLine("entry/src/main/ets/pages/PhotoScanPage.ets(285:9)", "entry");
            Button.width('45%');
            Button.height(48);
            Button.fontSize(16);
            Button.backgroundColor('#1976D2');
            Button.borderRadius(8);
            Button.enabled(this.scanResult !== null);
            Button.onClick(() => {
                this.confirmAndSave();
            });
        }, Button);
        Button.pop();
        // 底部按钮
        Row.pop();
        Column.pop();
    }
    // 事件处理方法
    private async capturePhoto() {
        try {
            this.isScanning = true;
            this.errorMessage = '';
            // 模拟拍照过程
            await this.delay(500);
            this.capturedImage = 'mock_image_path.jpg';
            // 调用AI识别
            this.scanResult = await this.aiService.recognizeReceipt(this.capturedImage);
            this.showResult = true;
        }
        catch (error) {
            this.errorMessage = '识别失败，请重试';
        }
        finally {
            this.isScanning = false;
        }
    }
    private selectFromGallery() {
        console.log('从相册选择');
        // TODO: 实现从相册选择图片
    }
    private toggleFlash() {
        console.log('切换闪光灯');
        // TODO: 实现闪光灯切换
    }
    private showHelpDialog() {
        console.log('显示帮助信息');
        // TODO: 显示拍照识别帮助信息
    }
    private resetScan() {
        this.showResult = false;
        this.scanResult = null;
        this.errorMessage = '';
        this.capturedImage = '';
    }
    private confirmAndSave() {
        if (!this.scanResult)
            return;
        // 创建账单对象
        const account = new Account();
        account.amount = this.scanResult.totalAmount;
        account.type = AccountType.EXPENSE;
        account.category = this.scanResult.category;
        account.description = this.scanResult.merchantName;
        account.date = this.scanResult.date;
        account.isAIGenerated = true;
        account.aiConfidence = this.scanResult.confidence;
        console.log('保存AI识别账单:', account);
        // TODO: 保存到数据库
        // 返回主页
        router.back();
    }
    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "PhotoScanPage";
    }
}
registerNamedRoute(() => new PhotoScanPage(undefined, {}), "", { bundleName: "com.silence.monter", moduleName: "entry", pagePath: "pages/PhotoScanPage", pageFullPath: "entry/src/main/ets/pages/PhotoScanPage", integratedHsp: "false", moduleType: "followWithHap" });
