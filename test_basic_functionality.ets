/**
 * 基础功能测试
 * 这个文件用于验证我们的核心类和方法是否正常工作
 */

import { Account, AccountType, AccountSummary, AccountData } from './entry/src/main/ets/models/Account';
import { Category, DefaultCategories, CategoryType, CategoryData } from './entry/src/main/ets/models/Category';
import { User, UserSettings, UserData } from './entry/src/main/ets/models/User';
import { FormatUtils } from './entry/src/main/ets/common/utils/FormatUtils';
import { DateUtils } from './entry/src/main/ets/common/utils/DateUtils';

/**
 * 测试Account模型
 */
function testAccountModel(): boolean {
  console.log('测试Account模型...');
  
  try {
    // 测试创建账单
    const accountData: AccountData = {
      amount: 25.50,
      type: AccountType.EXPENSE,
      category: '餐饮',
      description: '午餐'
    };
    
    const account = new Account(accountData);
    
    // 验证属性
    if (account.amount !== 25.50) {
      console.error('金额设置失败');
      return false;
    }
    
    if (account.type !== AccountType.EXPENSE) {
      console.error('类型设置失败');
      return false;
    }
    
    if (account.category !== '餐饮') {
      console.error('分类设置失败');
      return false;
    }
    
    // 测试格式化方法
    const formattedAmount = account.getFormattedAmount();
    if (formattedAmount !== '-¥25.50') {
      console.error('金额格式化失败:', formattedAmount);
      return false;
    }
    
    const amountColor = account.getAmountColor();
    if (amountColor !== '#F44336') {
      console.error('颜色获取失败:', amountColor);
      return false;
    }
    
    console.log('Account模型测试通过');
    return true;
    
  } catch (error) {
    console.error('Account模型测试失败:', error);
    return false;
  }
}

/**
 * 测试AccountSummary
 */
function testAccountSummary(): boolean {
  console.log('测试AccountSummary...');
  
  try {
    const accounts: Account[] = [];
    
    // 创建测试账单
    const income = new Account();
    income.amount = 1000;
    income.type = AccountType.INCOME;
    accounts.push(income);
    
    const expense1 = new Account();
    expense1.amount = 300;
    expense1.type = AccountType.EXPENSE;
    accounts.push(expense1);
    
    const expense2 = new Account();
    expense2.amount = 200;
    expense2.type = AccountType.EXPENSE;
    accounts.push(expense2);
    
    const summary = new AccountSummary(accounts);
    
    // 验证统计结果
    if (summary.totalIncome !== 1000) {
      console.error('总收入计算错误:', summary.totalIncome);
      return false;
    }
    
    if (summary.totalExpense !== 500) {
      console.error('总支出计算错误:', summary.totalExpense);
      return false;
    }
    
    if (summary.balance !== 500) {
      console.error('余额计算错误:', summary.balance);
      return false;
    }
    
    if (summary.accountCount !== 3) {
      console.error('账单数量计算错误:', summary.accountCount);
      return false;
    }
    
    console.log('AccountSummary测试通过');
    return true;
    
  } catch (error) {
    console.error('AccountSummary测试失败:', error);
    return false;
  }
}

/**
 * 测试Category模型
 */
function testCategoryModel(): boolean {
  console.log('测试Category模型...');
  
  try {
    const categoryData: CategoryData = {
      name: '餐饮',
      icon: '🍽️',
      color: '#FF9800',
      type: CategoryType.EXPENSE
    };
    
    const category = new Category(categoryData);
    
    if (category.name !== '餐饮') {
      console.error('分类名称设置失败');
      return false;
    }
    
    if (category.icon !== '🍽️') {
      console.error('分类图标设置失败');
      return false;
    }
    
    // 测试默认分类
    const expenseCategories = DefaultCategories.getExpenseCategories();
    if (expenseCategories.length === 0) {
      console.error('默认支出分类获取失败');
      return false;
    }
    
    const incomeCategories = DefaultCategories.getIncomeCategories();
    if (incomeCategories.length === 0) {
      console.error('默认收入分类获取失败');
      return false;
    }
    
    console.log('Category模型测试通过');
    return true;
    
  } catch (error) {
    console.error('Category模型测试失败:', error);
    return false;
  }
}

/**
 * 测试FormatUtils
 */
function testFormatUtils(): boolean {
  console.log('测试FormatUtils...');
  
  try {
    // 测试金额格式化
    const amount1 = FormatUtils.formatAmount(25.50);
    if (amount1 !== '¥25.50') {
      console.error('金额格式化失败:', amount1);
      return false;
    }
    
    const amount2 = FormatUtils.formatAmount(25.50, false);
    if (amount2 !== '25.50') {
      console.error('无符号金额格式化失败:', amount2);
      return false;
    }
    
    // 测试百分比格式化
    const percentage = FormatUtils.formatPercentage(25, 100);
    if (percentage !== '25.0%') {
      console.error('百分比格式化失败:', percentage);
      return false;
    }
    
    // 测试邮箱验证
    const validEmail = FormatUtils.isValidEmail('<EMAIL>');
    if (!validEmail) {
      console.error('有效邮箱验证失败');
      return false;
    }
    
    const invalidEmail = FormatUtils.isValidEmail('invalid-email');
    if (invalidEmail) {
      console.error('无效邮箱验证失败');
      return false;
    }
    
    console.log('FormatUtils测试通过');
    return true;
    
  } catch (error) {
    console.error('FormatUtils测试失败:', error);
    return false;
  }
}

/**
 * 测试DateUtils
 */
function testDateUtils(): boolean {
  console.log('测试DateUtils...');
  
  try {
    const now = new Date();
    
    // 测试日期格式化
    const formatted = DateUtils.formatDate(now, 'YYYY-MM-DD');
    if (!formatted.includes('-')) {
      console.error('日期格式化失败:', formatted);
      return false;
    }
    
    // 测试今天判断
    const isToday = DateUtils.isToday(now);
    if (!isToday) {
      console.error('今天判断失败');
      return false;
    }
    
    // 测试相对时间
    const relativeTime = DateUtils.getRelativeTime(now);
    if (relativeTime !== '刚刚') {
      console.error('相对时间计算失败:', relativeTime);
      return false;
    }
    
    console.log('DateUtils测试通过');
    return true;
    
  } catch (error) {
    console.error('DateUtils测试失败:', error);
    return false;
  }
}

/**
 * 运行所有测试
 */
function runAllTests(): void {
  console.log('开始运行基础功能测试...');
  
  const tests = [
    testAccountModel,
    testAccountSummary,
    testCategoryModel,
    testFormatUtils,
    testDateUtils
  ];
  
  let passedCount = 0;
  let totalCount = tests.length;
  
  for (const test of tests) {
    if (test()) {
      passedCount++;
    }
  }
  
  console.log(`测试完成: ${passedCount}/${totalCount} 通过`);
  
  if (passedCount === totalCount) {
    console.log('🎉 所有基础功能测试通过！');
  } else {
    console.log('❌ 部分测试失败，请检查代码');
  }
}

// 如果直接运行此文件，执行测试
// runAllTests();

export { runAllTests };
