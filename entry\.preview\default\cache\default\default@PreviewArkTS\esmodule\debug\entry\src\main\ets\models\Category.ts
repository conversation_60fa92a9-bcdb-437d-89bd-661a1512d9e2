/**
 * 分类数据模型
 */
export class Category {
    id: string = '';
    name: string = '';
    icon: string = '';
    color: string = '';
    type: CategoryType = CategoryType.EXPENSE;
    parentId?: string;
    isDefault: boolean = false;
    sortOrder: number = 0;
    constructor(data?: Partial<Category>) {
        if (data) {
            Object.assign(this, data);
        }
        if (!this.id) {
            this.id = this.generateId();
        }
    }
    private generateId(): string {
        return Date.now().toString() + Math.random().toString(36).substring(2, 11);
    }
}
/**
 * 分类类型枚举
 */
export enum CategoryType {
    INCOME = "income",
    EXPENSE = "expense" // 支出分类
}
/**
 * 默认分类配置
 */
export class DefaultCategories {
    static getExpenseCategories(): Category[] {
        const categories: Category[] = [];
        const category1 = new Category();
        category1.name = '餐饮';
        category1.icon = '🍽️';
        category1.color = '#FF9800';
        category1.type = CategoryType.EXPENSE;
        category1.isDefault = true;
        category1.sortOrder = 1;
        categories.push(category1);
        const category2 = new Category();
        category2.name = '交通';
        category2.icon = '🚗';
        category2.color = '#2196F3';
        category2.type = CategoryType.EXPENSE;
        category2.isDefault = true;
        category2.sortOrder = 2;
        categories.push(category2);
        const category3 = new Category();
        category3.name = '购物';
        category3.icon = '🛍️';
        category3.color = '#E91E63';
        category3.type = CategoryType.EXPENSE;
        category3.isDefault = true;
        category3.sortOrder = 3;
        categories.push(category3);
        const category4 = new Category();
        category4.name = '其他';
        category4.icon = '📝';
        category4.color = '#9E9E9E';
        category4.type = CategoryType.EXPENSE;
        category4.isDefault = true;
        category4.sortOrder = 99;
        categories.push(category4);
        return categories;
    }
    static getIncomeCategories(): Category[] {
        const categories: Category[] = [];
        const category1 = new Category();
        category1.name = '工资';
        category1.icon = '💰';
        category1.color = '#4CAF50';
        category1.type = CategoryType.INCOME;
        category1.isDefault = true;
        category1.sortOrder = 1;
        categories.push(category1);
        const category2 = new Category();
        category2.name = '其他';
        category2.icon = '📝';
        category2.color = '#9E9E9E';
        category2.type = CategoryType.INCOME;
        category2.isDefault = true;
        category2.sortOrder = 99;
        categories.push(category2);
        return categories;
    }
}
