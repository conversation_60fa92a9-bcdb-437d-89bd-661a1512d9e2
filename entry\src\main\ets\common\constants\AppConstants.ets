/**
 * 应用常量定义
 */
export class AppConstants {
  // 应用信息
  static readonly APP_NAME = 'MonterAI';
  static readonly APP_VERSION = '1.0.0';
  
  // 数据库相关
  static readonly DB_NAME = 'monter_ai.db';
  static readonly DB_VERSION = 1;
  
  // 表名
  static readonly TABLE_ACCOUNTS = 'accounts';
  static readonly TABLE_CATEGORIES = 'categories';
  static readonly TABLE_USERS = 'users';
  static readonly TABLE_BUDGETS = 'budgets';
  
  // 存储键名
  static readonly STORAGE_USER_SETTINGS = 'user_settings';
  static readonly STORAGE_LAST_SYNC = 'last_sync';
  static readonly STORAGE_APP_CONFIG = 'app_config';
  
  // AI服务相关
  static readonly AI_OCR_CONFIDENCE_THRESHOLD = 0.7;
  static readonly AI_VOICE_TIMEOUT = 10000; // 10秒
  static readonly AI_ANALYSIS_CACHE_TIME = 3600000; // 1小时
  
  // 网络相关
  static readonly REQUEST_TIMEOUT = 30000; // 30秒
  static readonly MAX_RETRY_COUNT = 3;
  
  // 文件相关
  static readonly MAX_IMAGE_SIZE = 5 * 1024 * 1024; // 5MB
  static readonly SUPPORTED_IMAGE_FORMATS = ['jpg', 'jpeg', 'png', 'webp'];
  
  // 分页相关
  static readonly DEFAULT_PAGE_SIZE = 20;
  static readonly MAX_PAGE_SIZE = 100;
  
  // 日期格式
  static readonly DATE_FORMAT = 'YYYY-MM-DD';
  static readonly DATETIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';
  static readonly TIME_FORMAT = 'HH:mm';
  
  // 货币格式
  static readonly CURRENCY_SYMBOL = '¥';
  static readonly DECIMAL_PLACES = 2;
  
  // 颜色主题
  static readonly COLORS = {
    PRIMARY: '#1976D2',
    SECONDARY: '#424242',
    SUCCESS: '#4CAF50',
    WARNING: '#FF9800',
    ERROR: '#F44336',
    INFO: '#2196F3',
    INCOME: '#4CAF50',
    EXPENSE: '#F44336',
    BACKGROUND: '#FAFAFA',
    SURFACE: '#FFFFFF',
    TEXT_PRIMARY: '#212121',
    TEXT_SECONDARY: '#757575'
  };
  
  // 动画时长
  static readonly ANIMATION_DURATION = {
    SHORT: 200,
    MEDIUM: 300,
    LONG: 500
  };
  
  // 路由路径
  static readonly ROUTES = {
    MAIN: '/pages/MainPage',
    ADD_ACCOUNT: '/pages/AddAccountPage',
    ANALYSIS: '/pages/AnalysisPage',
    SETTINGS: '/pages/SettingsPage',
    CATEGORY_MANAGE: '/pages/CategoryManagePage',
    BUDGET_MANAGE: '/pages/BudgetManagePage'
  };
  
  // 权限相关
  static readonly PERMISSIONS = {
    CAMERA: 'ohos.permission.CAMERA',
    MICROPHONE: 'ohos.permission.MICROPHONE',
    READ_MEDIA: 'ohos.permission.READ_MEDIA',
    WRITE_MEDIA: 'ohos.permission.WRITE_MEDIA',
    INTERNET: 'ohos.permission.INTERNET',
    LOCATION: 'ohos.permission.LOCATION'
  };
  
  // 事件名称
  static readonly EVENTS = {
    ACCOUNT_ADDED: 'account_added',
    ACCOUNT_UPDATED: 'account_updated',
    ACCOUNT_DELETED: 'account_deleted',
    CATEGORY_CHANGED: 'category_changed',
    SETTINGS_CHANGED: 'settings_changed',
    SYNC_COMPLETED: 'sync_completed'
  };
}
