if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface AnalysisPage_Params {
    analysis?: SpendingAnalysis | null;
    isLoading?: boolean;
    selectedPeriod?: string;
    accounts?: Account[];
    aiService?: AIService;
}
import { Account, AccountType } from "@normalized:N&&&entry/src/main/ets/models/Account&";
import { AIService } from "@normalized:N&&&entry/src/main/ets/services/AIService&";
import type { SpendingAnalysis, CategoryBreakdown, TrendData } from "@normalized:N&&&entry/src/main/ets/services/AIService&";
import { FormatUtils } from "@normalized:N&&&entry/src/main/ets/common/utils/FormatUtils&";
import { SimplePieChart } from "@normalized:N&&&entry/src/main/ets/components/PieChart&";
import type { ChartData } from "@normalized:N&&&entry/src/main/ets/components/PieChart&";
import router from "@ohos:router";
class AnalysisPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__analysis = new ObservedPropertyObjectPU(null, this, "analysis");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__selectedPeriod = new ObservedPropertySimplePU('本月', this, "selectedPeriod");
        this.__accounts = new ObservedPropertyObjectPU([], this, "accounts");
        this.aiService = AIService.getInstance();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: AnalysisPage_Params) {
        if (params.analysis !== undefined) {
            this.analysis = params.analysis;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.selectedPeriod !== undefined) {
            this.selectedPeriod = params.selectedPeriod;
        }
        if (params.accounts !== undefined) {
            this.accounts = params.accounts;
        }
        if (params.aiService !== undefined) {
            this.aiService = params.aiService;
        }
    }
    updateStateVars(params: AnalysisPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__analysis.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedPeriod.purgeDependencyOnElmtId(rmElmtId);
        this.__accounts.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__analysis.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__selectedPeriod.aboutToBeDeleted();
        this.__accounts.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __analysis: ObservedPropertyObjectPU<SpendingAnalysis | null>;
    get analysis() {
        return this.__analysis.get();
    }
    set analysis(newValue: SpendingAnalysis | null) {
        this.__analysis.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __selectedPeriod: ObservedPropertySimplePU<string>;
    get selectedPeriod() {
        return this.__selectedPeriod.get();
    }
    set selectedPeriod(newValue: string) {
        this.__selectedPeriod.set(newValue);
    }
    private __accounts: ObservedPropertyObjectPU<Account[]>;
    get accounts() {
        return this.__accounts.get();
    }
    set accounts(newValue: Account[]) {
        this.__accounts.set(newValue);
    }
    private aiService: AIService;
    aboutToAppear() {
        this.loadAnalysis();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(22:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        // 顶部导航栏
        this.buildHeader.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 内容区域
            if (this.isLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.buildLoadingView.bind(this)();
                });
            }
            else if (this.analysis) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.buildAnalysisContent.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.buildEmptyView.bind(this)();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    buildHeader(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(42:5)", "entry");
            Row.width('100%');
            Row.padding({ left: 16, right: 16, top: 12, bottom: 12 });
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 返回按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(44:7)", "entry");
            // 返回按钮
            Row.width(40);
            // 返回按钮
            Row.height(40);
            // 返回按钮
            Row.borderRadius(20);
            // 返回按钮
            Row.justifyContent(FlexAlign.Center);
            // 返回按钮
            Row.alignItems(VerticalAlign.Center);
            // 返回按钮
            Row.onClick(() => {
                router.back();
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('←');
            Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(45:9)", "entry");
            Text.fontSize(20);
            Text.fontColor('#212121');
        }, Text);
        Text.pop();
        // 返回按钮
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('智能分析');
            Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(58:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 刷新按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(66:7)", "entry");
            // 刷新按钮
            Row.width(40);
            // 刷新按钮
            Row.height(40);
            // 刷新按钮
            Row.borderRadius(20);
            // 刷新按钮
            Row.justifyContent(FlexAlign.Center);
            // 刷新按钮
            Row.alignItems(VerticalAlign.Center);
            // 刷新按钮
            Row.onClick(() => {
                this.loadAnalysis();
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🔄');
            Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(67:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#212121');
        }, Text);
        Text.pop();
        // 刷新按钮
        Row.pop();
        Row.pop();
    }
    buildLoadingView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(87:5)", "entry");
            Column.width('100%');
            Column.layoutWeight(1);
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🤖');
            Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(88:7)", "entry");
            Text.fontSize(60);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('AI正在分析中...');
            Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(92:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#757575');
        }, Text);
        Text.pop();
        Column.pop();
    }
    buildEmptyView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(104:5)", "entry");
            Column.width('100%');
            Column.layoutWeight(1);
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📊');
            Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(105:7)", "entry");
            Text.fontSize(60);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('暂无数据');
            Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(109:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#757575');
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('添加一些记录后再来查看分析吧');
            Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(114:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#9E9E9E');
        }, Text);
        Text.pop();
        Column.pop();
    }
    buildAnalysisContent(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(126:5)", "entry");
            Scroll.layoutWeight(1);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(127:7)", "entry");
            Column.padding(16);
        }, Column);
        // 总支出卡片
        this.buildTotalExpenseCard.bind(this)();
        // 分类支出分析
        this.buildCategoryAnalysis.bind(this)();
        // 分类支出图表
        this.buildCategoryChart.bind(this)();
        // 趋势分析
        this.buildTrendAnalysis.bind(this)();
        // AI洞察
        this.buildInsights.bind(this)();
        // AI建议
        this.buildRecommendations.bind(this)();
        Column.pop();
        Scroll.pop();
    }
    buildTotalExpenseCard(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(153:5)", "entry");
            Column.width('100%');
            Column.padding(20);
            Column.backgroundColor('#FFFFFF');
            Column.borderRadius(12);
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`${this.selectedPeriod}总支出`);
            Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(154:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(FormatUtils.formatAmount(this.analysis!.totalExpense));
            Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(161:7)", "entry");
            Text.fontSize(32);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#F44336');
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(167:7)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💡');
            Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(168:9)", "entry");
            Text.fontSize(16);
            Text.margin({ right: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('AI分析了您的消费习惯');
            Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(172:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#757575');
        }, Text);
        Text.pop();
        Row.pop();
        Column.pop();
    }
    buildCategoryAnalysis(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(186:5)", "entry");
            Column.width('100%');
            Column.padding(20);
            Column.backgroundColor('#FFFFFF');
            Column.borderRadius(12);
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('分类支出分析');
            Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(187:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = (_item, index: number) => {
                const item = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(195:9)", "entry");
                    Column.width('100%');
                    Column.margin({ bottom: index < this.analysis!.categoryBreakdown.length - 1 ? 16 : 0 });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Row.create();
                    Row.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(196:11)", "entry");
                    Row.width('100%');
                    Row.margin({ bottom: 8 });
                }, Row);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(this.getCategoryIcon(item.category));
                    Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(197:13)", "entry");
                    Text.fontSize(20);
                    Text.margin({ right: 12 });
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(201:13)", "entry");
                    Column.layoutWeight(1);
                    Column.alignItems(HorizontalAlign.Start);
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(item.category);
                    Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(202:15)", "entry");
                    Text.fontSize(14);
                    Text.fontColor('#212121');
                    Text.alignSelf(ItemAlign.Start);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(FormatUtils.formatAmount(item.amount));
                    Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(207:15)", "entry");
                    Text.fontSize(16);
                    Text.fontWeight(FontWeight.Bold);
                    Text.fontColor('#212121');
                    Text.alignSelf(ItemAlign.Start);
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(`${item.percentage.toFixed(1)}%`);
                    Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(216:13)", "entry");
                    Text.fontSize(14);
                    Text.fontColor('#757575');
                }, Text);
                Text.pop();
                Row.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    // 进度条
                    Row.create();
                    Row.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(224:11)", "entry");
                    // 进度条
                    Row.width('100%');
                    // 进度条
                    Row.height(4);
                    // 进度条
                    Row.backgroundColor('#E0E0E0');
                    // 进度条
                    Row.borderRadius(2);
                }, Row);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Row.create();
                    Row.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(225:13)", "entry");
                    Row.width(`${item.percentage}%`);
                    Row.height(4);
                    Row.backgroundColor(this.getCategoryColor(item.category));
                    Row.borderRadius(2);
                }, Row);
                Row.pop();
                // 进度条
                Row.pop();
                Column.pop();
            };
            this.forEachUpdateFunction(elmtId, this.analysis!.categoryBreakdown, forEachItemGenFunction, undefined, true, false);
        }, ForEach);
        ForEach.pop();
        Column.pop();
    }
    buildCategoryChart(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(249:5)", "entry");
            Column.width('100%');
            Column.padding(20);
            Column.backgroundColor('#FFFFFF');
            Column.borderRadius(12);
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支出分布');
            Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(250:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.analysis!.categoryBreakdown.length > 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new SimplePieChart(this, {
                                    data: this.getCategoryChartData(),
                                    size: 200
                                }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/AnalysisPage.ets", line: 258, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        data: this.getCategoryChartData(),
                                        size: 200
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    data: this.getCategoryChartData(),
                                    size: 200
                                });
                            }
                        }, { name: "SimplePieChart" });
                    }
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(263:9)", "entry");
                        Column.width('100%');
                        Column.height(200);
                        Column.justifyContent(FlexAlign.Center);
                        Column.alignItems(HorizontalAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('📊');
                        Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(264:11)", "entry");
                        Text.fontSize(48);
                        Text.margin({ bottom: 12 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无数据');
                        Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(268:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#757575');
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    buildTrendAnalysis(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(287:5)", "entry");
            Column.width('100%');
            Column.padding(20);
            Column.backgroundColor('#FFFFFF');
            Column.borderRadius(12);
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('趋势分析');
            Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(288:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = (_item, index: number) => {
                const trend = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Row.create();
                    Row.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(296:9)", "entry");
                    Row.width('100%');
                    Row.margin({ bottom: index < this.analysis!.trends.length - 1 ? 12 : 0 });
                }, Row);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(trend.period);
                    Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(297:11)", "entry");
                    Text.fontSize(14);
                    Text.fontColor('#757575');
                    Text.width(60);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(FormatUtils.formatAmount(trend.amount));
                    Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(302:11)", "entry");
                    Text.fontSize(14);
                    Text.fontColor('#212121');
                    Text.layoutWeight(1);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Row.create();
                    Row.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(307:11)", "entry");
                }, Row);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(trend.change > 0 ? '📈' : '📉');
                    Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(308:13)", "entry");
                    Text.fontSize(14);
                    Text.margin({ right: 4 });
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(`${Math.abs(trend.change)}%`);
                    Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(312:13)", "entry");
                    Text.fontSize(14);
                    Text.fontColor(trend.change > 0 ? '#F44336' : '#4CAF50');
                }, Text);
                Text.pop();
                Row.pop();
                Row.pop();
            };
            this.forEachUpdateFunction(elmtId, this.analysis!.trends, forEachItemGenFunction, undefined, true, false);
        }, ForEach);
        ForEach.pop();
        Column.pop();
    }
    buildInsights(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(330:5)", "entry");
            Column.width('100%');
            Column.padding(20);
            Column.backgroundColor('#E3F2FD');
            Column.borderRadius(12);
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(331:7)", "entry");
            Row.alignSelf(ItemAlign.Start);
            Row.margin({ bottom: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🔍');
            Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(332:9)", "entry");
            Text.fontSize(20);
            Text.margin({ right: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('AI洞察');
            Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(336:9)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = (_item, index: number) => {
                const insight = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Row.create();
                    Row.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(345:9)", "entry");
                    Row.width('100%');
                    Row.alignItems(VerticalAlign.Top);
                    Row.margin({ bottom: index < this.analysis!.insights.length - 1 ? 8 : 0 });
                }, Row);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('•');
                    Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(346:11)", "entry");
                    Text.fontSize(16);
                    Text.fontColor('#1976D2');
                    Text.margin({ right: 8 });
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(insight);
                    Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(351:11)", "entry");
                    Text.fontSize(14);
                    Text.fontColor('#212121');
                    Text.layoutWeight(1);
                }, Text);
                Text.pop();
                Row.pop();
            };
            this.forEachUpdateFunction(elmtId, this.analysis!.insights, forEachItemGenFunction, undefined, true, false);
        }, ForEach);
        ForEach.pop();
        Column.pop();
    }
    buildRecommendations(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(370:5)", "entry");
            Column.width('100%');
            Column.padding(20);
            Column.backgroundColor('#E8F5E8');
            Column.borderRadius(12);
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(371:7)", "entry");
            Row.alignSelf(ItemAlign.Start);
            Row.margin({ bottom: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💡');
            Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(372:9)", "entry");
            Text.fontSize(20);
            Text.margin({ right: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('AI建议');
            Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(376:9)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = (_item, index: number) => {
                const recommendation = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Row.create();
                    Row.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(385:9)", "entry");
                    Row.width('100%');
                    Row.alignItems(VerticalAlign.Top);
                    Row.margin({ bottom: index < this.analysis!.recommendations.length - 1 ? 8 : 0 });
                }, Row);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('•');
                    Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(386:11)", "entry");
                    Text.fontSize(16);
                    Text.fontColor('#4CAF50');
                    Text.margin({ right: 8 });
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(recommendation);
                    Text.debugLine("entry/src/main/ets/pages/AnalysisPage.ets(391:11)", "entry");
                    Text.fontSize(14);
                    Text.fontColor('#212121');
                    Text.layoutWeight(1);
                }, Text);
                Text.pop();
                Row.pop();
            };
            this.forEachUpdateFunction(elmtId, this.analysis!.recommendations, forEachItemGenFunction, undefined, true, false);
        }, ForEach);
        ForEach.pop();
        Column.pop();
    }
    // 私有方法
    private async loadAnalysis() {
        this.isLoading = true;
        try {
            // 加载模拟数据
            this.loadMockAccounts();
            // 调用AI分析
            this.analysis = await this.aiService.analyzeSpendingHabits(this.accounts);
        }
        catch (error) {
            console.error('加载分析失败:', error);
        }
        finally {
            this.isLoading = false;
        }
    }
    private loadMockAccounts() {
        // 模拟账单数据
        this.accounts = [];
        const account1 = new Account();
        account1.amount = 25.50;
        account1.type = AccountType.EXPENSE;
        account1.category = '餐饮';
        this.accounts.push(account1);
        const account2 = new Account();
        account2.amount = 120.00;
        account2.type = AccountType.EXPENSE;
        account2.category = '交通';
        this.accounts.push(account2);
    }
    private getCategoryIcon(category: string): string {
        const iconMap: Record<string, string> = {
            '餐饮': '🍽️',
            '交通': '🚗',
            '购物': '🛍️',
            '娱乐': '🎮',
            '医疗': '🏥',
            '教育': '📚',
            '住房': '🏠'
        };
        return iconMap[category] || '📝';
    }
    private getCategoryColor(category: string): string {
        const colorMap: Record<string, string> = {
            '餐饮': '#FF9800',
            '交通': '#2196F3',
            '购物': '#E91E63',
            '娱乐': '#9C27B0',
            '医疗': '#F44336',
            '教育': '#607D8B',
            '住房': '#795548'
        };
        return colorMap[category] || '#9E9E9E';
    }
    private getCategoryChartData(): ChartData[] {
        return this.analysis!.categoryBreakdown.map(item => ({
            label: item.category,
            value: item.amount,
            color: this.getCategoryColor(item.category),
            percentage: item.percentage
        }));
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "AnalysisPage";
    }
}
registerNamedRoute(() => new AnalysisPage(undefined, {}), "", { bundleName: "com.silence.monter", moduleName: "entry", pagePath: "pages/AnalysisPage", pageFullPath: "entry/src/main/ets/pages/AnalysisPage", integratedHsp: "false", moduleType: "followWithHap" });
