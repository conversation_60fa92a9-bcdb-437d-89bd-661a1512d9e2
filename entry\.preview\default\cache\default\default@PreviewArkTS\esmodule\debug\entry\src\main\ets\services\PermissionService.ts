import abilityAccessCtrl from "@ohos:abilityAccessCtrl";
import type { Permissions } from "@ohos:abilityAccessCtrl";
import type { Context } from "@ohos:abilityAccessCtrl";
import { AppConstants } from "@normalized:N&&&entry/src/main/ets/common/constants/AppConstants&";
/**
 * 权限管理服务
 */
export class PermissionService {
    private static instance: PermissionService;
    private atManager: abilityAccessCtrl.AtManager;
    private context: Context | null = null;
    private constructor() {
        this.atManager = abilityAccessCtrl.createAtManager();
    }
    static getInstance(): PermissionService {
        if (!PermissionService.instance) {
            PermissionService.instance = new PermissionService();
        }
        return PermissionService.instance;
    }
    /**
     * 设置上下文
     */
    setContext(context: Context): void {
        this.context = context;
    }
    /**
     * 检查权限状态
     */
    async checkPermission(permission: Permissions): Promise<PermissionStatus> {
        try {
            if (!this.context) {
                console.log('Context未设置，返回模拟权限状态');
                return PermissionStatus.GRANTED;
            }
            const result = await this.atManager.checkAccessToken(this.context.applicationInfo.accessTokenId, permission);
            switch (result) {
                case abilityAccessCtrl.GrantStatus.PERMISSION_GRANTED:
                    return PermissionStatus.GRANTED;
                case abilityAccessCtrl.GrantStatus.PERMISSION_DENIED:
                    return PermissionStatus.DENIED;
                default:
                    return PermissionStatus.UNKNOWN;
            }
        }
        catch (error) {
            console.error('检查权限失败:', error);
            return PermissionStatus.UNKNOWN;
        }
    }
    /**
     * 请求单个权限
     */
    async requestPermission(permission: Permissions): Promise<PermissionResult> {
        try {
            console.log('请求权限:', permission);
            if (!this.context) {
                console.log('Context未设置，返回模拟权限结果');
                return {
                    permission: permission,
                    status: PermissionStatus.GRANTED,
                    message: '模拟权限授权成功'
                };
            }
            // 先检查当前状态
            const currentStatus = await this.checkPermission(permission);
            if (currentStatus === PermissionStatus.GRANTED) {
                return {
                    permission: permission,
                    status: PermissionStatus.GRANTED,
                    message: '权限已授权'
                };
            }
            // 请求权限
            const result = await this.atManager.requestPermissionsFromUser(this.context, [permission]);
            if (result.authResults && result.authResults.length > 0) {
                const authResult = result.authResults[0];
                const status = authResult === abilityAccessCtrl.GrantStatus.PERMISSION_GRANTED
                    ? PermissionStatus.GRANTED
                    : PermissionStatus.DENIED;
                return {
                    permission: permission,
                    status: status,
                    message: status === PermissionStatus.GRANTED ? '权限授权成功' : '权限被拒绝'
                };
            }
            return {
                permission: permission,
                status: PermissionStatus.UNKNOWN,
                message: '权限请求结果未知'
            };
        }
        catch (error) {
            console.error('请求权限失败:', error);
            return {
                permission: permission,
                status: PermissionStatus.ERROR,
                message: '权限请求失败'
            };
        }
    }
    /**
     * 请求多个权限
     */
    async requestPermissions(permissions: Permissions[]): Promise<PermissionResult[]> {
        try {
            console.log('请求多个权限:', permissions);
            if (!this.context) {
                console.log('Context未设置，返回模拟权限结果');
                return permissions.map(permission => ({
                    permission: permission,
                    status: PermissionStatus.GRANTED,
                    message: '模拟权限授权成功'
                }));
            }
            const results: PermissionResult[] = [];
            // 检查已授权的权限
            const needRequestPermissions: Permissions[] = [];
            for (const permission of permissions) {
                const status = await this.checkPermission(permission);
                if (status === PermissionStatus.GRANTED) {
                    results.push({
                        permission: permission,
                        status: PermissionStatus.GRANTED,
                        message: '权限已授权'
                    });
                }
                else {
                    needRequestPermissions.push(permission);
                }
            }
            // 请求未授权的权限
            if (needRequestPermissions.length > 0) {
                const result = await this.atManager.requestPermissionsFromUser(this.context, needRequestPermissions);
                if (result.authResults && result.authResults.length > 0) {
                    for (let i = 0; i < needRequestPermissions.length; i++) {
                        const authResult = result.authResults[i];
                        const status = authResult === abilityAccessCtrl.GrantStatus.PERMISSION_GRANTED
                            ? PermissionStatus.GRANTED
                            : PermissionStatus.DENIED;
                        results.push({
                            permission: needRequestPermissions[i],
                            status: status,
                            message: status === PermissionStatus.GRANTED ? '权限授权成功' : '权限被拒绝'
                        });
                    }
                }
            }
            return results;
        }
        catch (error) {
            console.error('请求多个权限失败:', error);
            return permissions.map(permission => ({
                permission: permission,
                status: PermissionStatus.ERROR,
                message: '权限请求失败'
            }));
        }
    }
    /**
     * 检查相机权限
     */
    async checkCameraPermission(): Promise<PermissionStatus> {
        return await this.checkPermission(AppConstants.PERMISSION_CAMERA as Permissions);
    }
    /**
     * 请求相机权限
     */
    async requestCameraPermission(): Promise<PermissionResult> {
        return await this.requestPermission(AppConstants.PERMISSION_CAMERA as Permissions);
    }
    /**
     * 检查麦克风权限
     */
    async checkMicrophonePermission(): Promise<PermissionStatus> {
        return await this.checkPermission(AppConstants.PERMISSION_MICROPHONE as Permissions);
    }
    /**
     * 请求麦克风权限
     */
    async requestMicrophonePermission(): Promise<PermissionResult> {
        return await this.requestPermission(AppConstants.PERMISSION_MICROPHONE as Permissions);
    }
    /**
     * 检查存储权限
     */
    async checkStoragePermission(): Promise<PermissionStatus> {
        return await this.checkPermission(AppConstants.PERMISSION_READ_MEDIA as Permissions);
    }
    /**
     * 请求存储权限
     */
    async requestStoragePermission(): Promise<PermissionResult> {
        const permissions = [
            AppConstants.PERMISSION_READ_MEDIA as Permissions,
            AppConstants.PERMISSION_WRITE_MEDIA as Permissions
        ];
        const results = await this.requestPermissions(permissions);
        // 返回读取权限的结果（通常读写权限一起授权）
        return results.find(r => r.permission === AppConstants.PERMISSION_READ_MEDIA) || results[0];
    }
    /**
     * 检查位置权限
     */
    async checkLocationPermission(): Promise<PermissionStatus> {
        return await this.checkPermission(AppConstants.PERMISSION_LOCATION as Permissions);
    }
    /**
     * 请求位置权限
     */
    async requestLocationPermission(): Promise<PermissionResult> {
        return await this.requestPermission(AppConstants.PERMISSION_LOCATION as Permissions);
    }
    /**
     * 获取权限描述
     */
    getPermissionDescription(permission: string): string {
        const descriptions: Record<string, string> = {
            [AppConstants.PERMISSION_CAMERA]: '相机权限用于拍照识别小票功能',
            [AppConstants.PERMISSION_MICROPHONE]: '麦克风权限用于语音记账功能',
            [AppConstants.PERMISSION_READ_MEDIA]: '存储权限用于读取和保存图片',
            [AppConstants.PERMISSION_WRITE_MEDIA]: '存储权限用于保存数据和图片',
            [AppConstants.PERMISSION_LOCATION]: '位置权限用于记录消费地点',
            [AppConstants.PERMISSION_INTERNET]: '网络权限用于数据同步和AI服务'
        };
        return descriptions[permission] || '该权限用于应用正常功能';
    }
    /**
     * 检查是否需要显示权限说明
     */
    shouldShowPermissionRationale(permission: Permissions): boolean {
        // 在实际应用中，这里可以根据用户之前的拒绝行为来判断
        // 目前返回false，表示直接请求权限
        return false;
    }
    /**
     * 打开应用设置页面
     */
    async openAppSettings(): Promise<void> {
        try {
            console.log('打开应用设置页面');
            // TODO: 实现跳转到应用设置页面的逻辑
            // 在HarmonyOS中可能需要使用特定的API来打开设置
        }
        catch (error) {
            console.error('打开应用设置失败:', error);
        }
    }
}
// 权限状态枚举
export enum PermissionStatus {
    GRANTED = "granted",
    DENIED = "denied",
    UNKNOWN = "unknown",
    ERROR = "error"
}
// 权限请求结果接口
export interface PermissionResult {
    permission: Permissions;
    status: PermissionStatus;
    message: string;
}
// 权限组合检查结果
export interface PermissionCheckResult {
    allGranted: boolean;
    results: PermissionResult[];
    deniedPermissions: Permissions[];
}
