import { AbilityConstant, ConfigurationConstant, UIAbility, Want } from '@kit.AbilityKit';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { window } from '@kit.ArkUI';
import { DatabaseService } from '../services/DatabaseService';
import { SyncService } from '../services/SyncService';

const DOMAIN = 0x0000;

export default class EntryAbility extends UIAbility {
  private databaseService: DatabaseService = DatabaseService.getInstance();
  private syncService: SyncService = SyncService.getInstance();

  onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    this.context.getApplicationContext().setColorMode(ConfigurationConstant.ColorMode.COLOR_MODE_NOT_SET);
    hilog.info(DOMAIN, 'MonterAI', '%{public}s', 'Ability onCreate');
    this.initializeServices();
  }

  onDestroy(): void {
    hilog.info(DOMAIN, 'testTag', '%{public}s', 'Ability onDestroy');
  }

  onWindowStageCreate(windowStage: window.WindowStage): void {
    // Main window is created, set main page for this ability
    hilog.info(DOMAIN, 'testTag', '%{public}s', 'Ability onWindowStageCreate');

    windowStage.loadContent('pages/Index', (err) => {
      if (err.code) {
        hilog.error(DOMAIN, 'testTag', 'Failed to load the content. Cause: %{public}s', JSON.stringify(err));
        return;
      }
      hilog.info(DOMAIN, 'testTag', 'Succeeded in loading the content.');
    });
  }

  onWindowStageDestroy(): void {
    // Main window is destroyed, release UI related resources
    hilog.info(DOMAIN, 'testTag', '%{public}s', 'Ability onWindowStageDestroy');
  }

  onForeground(): void {
    // Ability has brought to foreground
    hilog.info(DOMAIN, 'testTag', '%{public}s', 'Ability onForeground');
  }

  onBackground(): void {
    // Ability has back to background
    hilog.info(DOMAIN, 'MonterAI', '%{public}s', 'Ability onBackground');
  }

  /**
   * 初始化应用服务
   */
  private async initializeServices(): Promise<void> {
    try {
      hilog.info(DOMAIN, 'MonterAI', 'Initializing services...');

      // 初始化数据库服务
      await this.databaseService.initialize();

      // 初始化同步服务
      await this.syncService.initialize();

      hilog.info(DOMAIN, 'MonterAI', 'Services initialized successfully');

    } catch (error) {
      hilog.error(DOMAIN, 'MonterAI', 'Failed to initialize services: %{public}s', error.message);
    }
  }
}