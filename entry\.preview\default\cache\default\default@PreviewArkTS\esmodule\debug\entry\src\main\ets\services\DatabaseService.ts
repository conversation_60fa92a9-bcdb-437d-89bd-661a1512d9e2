import { Account, AccountType } from "@normalized:N&&&entry/src/main/ets/models/Account&";
import { Category } from "@normalized:N&&&entry/src/main/ets/models/Category&";
import { User } from "@normalized:N&&&entry/src/main/ets/models/User&";
import { AppConstants } from "@normalized:N&&&entry/src/main/ets/common/constants/AppConstants&";
import relationalStore from "@ohos:data.relationalStore";
/**
 * 数据库服务类
 */
export class DatabaseService {
    private static instance: DatabaseService;
    private store: relationalStore.RdbStore | null = null;
    private isInitialized: boolean = false;
    private constructor() { }
    static getInstance(): DatabaseService {
        if (!DatabaseService.instance) {
            DatabaseService.instance = new DatabaseService();
        }
        return DatabaseService.instance;
    }
    /**
     * 初始化数据库
     */
    async initialize(): Promise<void> {
        if (this.isInitialized)
            return;
        try {
            console.log('初始化数据库');
            const config: relationalStore.StoreConfig = {
                name: AppConstants.DB_NAME,
                securityLevel: relationalStore.SecurityLevel.S1
            };
            // 模拟数据库初始化
            await this.delay(500);
            console.log('数据库初始化完成');
            // 创建表
            await this.createTables();
            this.isInitialized = true;
        }
        catch (error) {
            console.error('数据库初始化失败:', error);
            throw new Error('数据库初始化失败');
        }
    }
    /**
     * 保存账单
     */
    async saveAccount(account: Account): Promise<boolean> {
        try {
            console.log('保存账单:', account.id);
            // 模拟数据库操作
            await this.delay(100);
            console.log('账单保存成功');
            return true;
        }
        catch (error) {
            console.error('保存账单失败:', error);
            return false;
        }
    }
    /**
     * 获取账单列表
     */
    async getAccounts(limit?: number, offset?: number): Promise<Account[]> {
        try {
            console.log('获取账单列表');
            // 模拟数据库查询
            await this.delay(200);
            // 返回模拟数据
            const accounts: Account[] = [];
            const account1 = new Account();
            account1.amount = 25.50;
            account1.type = AccountType.EXPENSE;
            account1.category = '餐饮';
            account1.description = '午餐';
            account1.date = new Date();
            account1.location = '公司附近';
            accounts.push(account1);
            const account2 = new Account();
            account2.amount = 8000.00;
            account2.type = AccountType.INCOME;
            account2.category = '工资';
            account2.description = '月薪';
            account2.date = new Date(Date.now() - 24 * 60 * 60 * 1000);
            accounts.push(account2);
            const account3 = new Account();
            account3.amount = 120.00;
            account3.type = AccountType.EXPENSE;
            account3.category = '交通';
            account3.description = '地铁卡充值';
            account3.date = new Date(Date.now() - 2 * 24 * 60 * 60 * 1000);
            accounts.push(account3);
            console.log('获取到账单:', accounts.length);
            return accounts;
        }
        catch (error) {
            console.error('获取账单列表失败:', error);
            return [];
        }
    }
    /**
     * 根据ID获取账单
     */
    async getAccountById(id: string): Promise<Account | null> {
        try {
            console.log('获取账单:', id);
            await this.delay(100);
            // 模拟查询结果
            const account = new Account();
            account.id = id;
            account.amount = 25.50;
            account.type = AccountType.EXPENSE;
            account.category = '餐饮';
            account.description = '午餐';
            return account;
        }
        catch (error) {
            console.error('获取账单失败:', error);
            return null;
        }
    }
    /**
     * 更新账单
     */
    async updateAccount(account: Account): Promise<boolean> {
        try {
            console.log('更新账单:', account.id);
            await this.delay(100);
            console.log('账单更新成功');
            return true;
        }
        catch (error) {
            console.error('更新账单失败:', error);
            return false;
        }
    }
    /**
     * 删除账单
     */
    async deleteAccount(id: string): Promise<boolean> {
        try {
            console.log('删除账单:', id);
            await this.delay(100);
            console.log('账单删除成功');
            return true;
        }
        catch (error) {
            console.error('删除账单失败:', error);
            return false;
        }
    }
    /**
     * 获取分类列表
     */
    async getCategories(): Promise<Category[]> {
        try {
            console.log('获取分类列表');
            await this.delay(100);
            // 返回默认分类
            const categories: Category[] = [];
            const cat1 = new Category();
            cat1.name = '餐饮';
            cat1.icon = '🍽️';
            cat1.color = '#FF9800';
            categories.push(cat1);
            const cat2 = new Category();
            cat2.name = '交通';
            cat2.icon = '🚗';
            cat2.color = '#2196F3';
            categories.push(cat2);
            console.log('获取到分类:', categories.length);
            return categories;
        }
        catch (error) {
            console.error('获取分类列表失败:', error);
            return [];
        }
    }
    /**
     * 保存分类
     */
    async saveCategory(category: Category): Promise<boolean> {
        try {
            console.log('保存分类:', category.name);
            await this.delay(100);
            console.log('分类保存成功');
            return true;
        }
        catch (error) {
            console.error('保存分类失败:', error);
            return false;
        }
    }
    /**
     * 获取用户信息
     */
    async getUser(): Promise<User | null> {
        try {
            console.log('获取用户信息');
            await this.delay(100);
            const user = new User();
            user.username = 'MonterAI用户';
            user.email = '<EMAIL>';
            return user;
        }
        catch (error) {
            console.error('获取用户信息失败:', error);
            return null;
        }
    }
    /**
     * 保存用户信息
     */
    async saveUser(user: User): Promise<boolean> {
        try {
            console.log('保存用户信息:', user.username);
            await this.delay(100);
            console.log('用户信息保存成功');
            return true;
        }
        catch (error) {
            console.error('保存用户信息失败:', error);
            return false;
        }
    }
    /**
     * 按日期范围查询账单
     */
    async getAccountsByDateRange(startDate: Date, endDate: Date): Promise<Account[]> {
        try {
            console.log('按日期范围查询账单:', startDate, endDate);
            await this.delay(200);
            // 模拟查询结果
            const accounts = await this.getAccounts();
            return accounts.filter(account => account.date >= startDate && account.date <= endDate);
        }
        catch (error) {
            console.error('按日期范围查询账单失败:', error);
            return [];
        }
    }
    /**
     * 按分类查询账单
     */
    async getAccountsByCategory(category: string): Promise<Account[]> {
        try {
            console.log('按分类查询账单:', category);
            await this.delay(200);
            const accounts = await this.getAccounts();
            return accounts.filter(account => account.category === category);
        }
        catch (error) {
            console.error('按分类查询账单失败:', error);
            return [];
        }
    }
    /**
     * 获取统计数据
     */
    async getStatistics(startDate: Date, endDate: Date): Promise<StatisticsData> {
        try {
            console.log('获取统计数据');
            await this.delay(300);
            const accounts = await this.getAccountsByDateRange(startDate, endDate);
            const totalIncome = accounts
                .filter(a => a.type === AccountType.INCOME)
                .reduce((sum, a) => sum + a.amount, 0);
            const totalExpense = accounts
                .filter(a => a.type === AccountType.EXPENSE)
                .reduce((sum, a) => sum + a.amount, 0);
            return {
                totalIncome,
                totalExpense,
                balance: totalIncome - totalExpense,
                accountCount: accounts.length,
                categoryStats: this.calculateCategoryStats(accounts)
            };
        }
        catch (error) {
            console.error('获取统计数据失败:', error);
            return {
                totalIncome: 0,
                totalExpense: 0,
                balance: 0,
                accountCount: 0,
                categoryStats: []
            };
        }
    }
    // 私有方法
    private async createTables(): Promise<void> {
        try {
            console.log('创建数据表');
            // 模拟创建表的过程
            await this.delay(200);
            console.log('数据表创建完成');
        }
        catch (error) {
            console.error('创建数据表失败:', error);
            throw new Error('创建数据表失败');
        }
    }
    private calculateCategoryStats(accounts: Account[]): CategoryStats[] {
        const categoryMap = new Map<string, number>();
        accounts
            .filter(a => a.type === AccountType.EXPENSE)
            .forEach(account => {
            const current = categoryMap.get(account.category) || 0;
            categoryMap.set(account.category, current + account.amount);
        });
        const result: CategoryStats[] = [];
        categoryMap.forEach((amount: number, category: string) => {
            const stats: CategoryStats = {
                category: category,
                amount: amount,
                count: accounts.filter(a => a.category === category).length
            };
            result.push(stats);
        });
        return result;
    }
    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
// 类型定义
export interface StatisticsData {
    totalIncome: number;
    totalExpense: number;
    balance: number;
    accountCount: number;
    categoryStats: CategoryStats[];
}
export interface CategoryStats {
    category: string;
    amount: number;
    count: number;
}
