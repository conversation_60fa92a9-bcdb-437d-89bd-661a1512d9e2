/**
 * 账单数据接口
 */
export interface AccountData {
    id?: string;
    amount?: number;
    type?: AccountType;
    category?: string;
    description?: string;
    date?: Date;
    location?: string;
    imageUrl?: string;
    tags?: string[];
    isAIGenerated?: boolean;
    aiConfidence?: number;
}
/**
 * 账单数据模型
 */
export class Account {
    id: string = '';
    amount: number = 0;
    type: AccountType = AccountType.EXPENSE;
    category: string = '';
    description: string = '';
    date: Date = new Date();
    location?: string;
    imageUrl?: string;
    tags: string[] = [];
    isAIGenerated: boolean = false;
    aiConfidence?: number;
    constructor(data?: AccountData) {
        if (data) {
            if (data.id !== undefined)
                this.id = data.id;
            if (data.amount !== undefined)
                this.amount = data.amount;
            if (data.type !== undefined)
                this.type = data.type;
            if (data.category !== undefined)
                this.category = data.category;
            if (data.description !== undefined)
                this.description = data.description;
            if (data.date !== undefined)
                this.date = data.date;
            if (data.location !== undefined)
                this.location = data.location;
            if (data.imageUrl !== undefined)
                this.imageUrl = data.imageUrl;
            if (data.tags !== undefined)
                this.tags = data.tags;
            if (data.isAIGenerated !== undefined)
                this.isAIGenerated = data.isAIGenerated;
            if (data.aiConfidence !== undefined)
                this.aiConfidence = data.aiConfidence;
        }
        if (!this.id) {
            this.id = this.generateId();
        }
    }
    private generateId(): string {
        return Date.now().toString() + Math.random().toString(36).substring(2, 11);
    }
    /**
     * 格式化金额显示
     */
    getFormattedAmount(): string {
        const prefix = this.type === AccountType.INCOME ? '+' : '-';
        return `${prefix}¥${this.amount.toFixed(2)}`;
    }
    /**
     * 获取账单颜色
     */
    getAmountColor(): string {
        return this.type === AccountType.INCOME ? '#4CAF50' : '#F44336';
    }
}
/**
 * 账单类型枚举
 */
export enum AccountType {
    INCOME = "income",
    EXPENSE = "expense" // 支出
}
/**
 * 账单统计数据
 */
export class AccountSummary {
    totalIncome: number = 0;
    totalExpense: number = 0;
    balance: number = 0;
    accountCount: number = 0;
    constructor(accounts: Account[]) {
        this.calculateSummary(accounts);
    }
    private calculateSummary(accounts: Account[]) {
        this.totalIncome = accounts
            .filter(account => account.type === AccountType.INCOME)
            .reduce((sum, account) => sum + account.amount, 0);
        this.totalExpense = accounts
            .filter(account => account.type === AccountType.EXPENSE)
            .reduce((sum, account) => sum + account.amount, 0);
        this.balance = this.totalIncome - this.totalExpense;
        this.accountCount = accounts.length;
    }
}
