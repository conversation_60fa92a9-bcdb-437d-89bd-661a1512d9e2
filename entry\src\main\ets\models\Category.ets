/**
 * 分类数据模型
 */
export class Category {
  id: string = '';
  name: string = '';
  icon: string = '';
  color: string = '';
  type: CategoryType = CategoryType.EXPENSE;
  parentId?: string;
  isDefault: boolean = false;
  sortOrder: number = 0;
  
  constructor(data?: Partial<Category>) {
    if (data) {
      Object.assign(this, data);
    }
    if (!this.id) {
      this.id = this.generateId();
    }
  }
  
  private generateId(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  }
}

/**
 * 分类类型枚举
 */
export enum CategoryType {
  INCOME = 'income',   // 收入分类
  EXPENSE = 'expense'  // 支出分类
}

/**
 * 默认分类配置
 */
export class DefaultCategories {
  static getExpenseCategories(): Category[] {
    return [
      new Category({
        name: '餐饮',
        icon: '🍽️',
        color: '#FF9800',
        type: CategoryType.EXPENSE,
        isDefault: true,
        sortOrder: 1
      }),
      new Category({
        name: '交通',
        icon: '🚗',
        color: '#2196F3',
        type: CategoryType.EXPENSE,
        isDefault: true,
        sortOrder: 2
      }),
      new Category({
        name: '购物',
        icon: '🛍️',
        color: '#E91E63',
        type: CategoryType.EXPENSE,
        isDefault: true,
        sortOrder: 3
      }),
      new Category({
        name: '娱乐',
        icon: '🎮',
        color: '#9C27B0',
        type: CategoryType.EXPENSE,
        isDefault: true,
        sortOrder: 4
      }),
      new Category({
        name: '医疗',
        icon: '🏥',
        color: '#F44336',
        type: CategoryType.EXPENSE,
        isDefault: true,
        sortOrder: 5
      }),
      new Category({
        name: '教育',
        icon: '📚',
        color: '#607D8B',
        type: CategoryType.EXPENSE,
        isDefault: true,
        sortOrder: 6
      }),
      new Category({
        name: '住房',
        icon: '🏠',
        color: '#795548',
        type: CategoryType.EXPENSE,
        isDefault: true,
        sortOrder: 7
      }),
      new Category({
        name: '其他',
        icon: '📝',
        color: '#9E9E9E',
        type: CategoryType.EXPENSE,
        isDefault: true,
        sortOrder: 99
      })
    ];
  }
  
  static getIncomeCategories(): Category[] {
    return [
      new Category({
        name: '工资',
        icon: '💰',
        color: '#4CAF50',
        type: CategoryType.INCOME,
        isDefault: true,
        sortOrder: 1
      }),
      new Category({
        name: '奖金',
        icon: '🎁',
        color: '#8BC34A',
        type: CategoryType.INCOME,
        isDefault: true,
        sortOrder: 2
      }),
      new Category({
        name: '投资',
        icon: '📈',
        color: '#CDDC39',
        type: CategoryType.INCOME,
        isDefault: true,
        sortOrder: 3
      }),
      new Category({
        name: '兼职',
        icon: '💼',
        color: '#FFC107',
        type: CategoryType.INCOME,
        isDefault: true,
        sortOrder: 4
      }),
      new Category({
        name: '其他',
        icon: '📝',
        color: '#9E9E9E',
        type: CategoryType.INCOME,
        isDefault: true,
        sortOrder: 99
      })
    ];
  }
}
