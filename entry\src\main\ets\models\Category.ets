/**
 * 分类数据接口
 */
export interface CategoryData {
  id?: string;
  name?: string;
  icon?: string;
  color?: string;
  type?: CategoryType;
  parentId?: string;
  isDefault?: boolean;
  sortOrder?: number;
}

/**
 * 分类数据模型
 */
export class Category {
  id: string = '';
  name: string = '';
  icon: string = '';
  color: string = '';
  type: CategoryType = CategoryType.EXPENSE;
  parentId?: string;
  isDefault: boolean = false;
  sortOrder: number = 0;
  
  constructor(data?: CategoryData) {
    if (data) {
      if (data.id !== undefined) this.id = data.id;
      if (data.name !== undefined) this.name = data.name;
      if (data.icon !== undefined) this.icon = data.icon;
      if (data.color !== undefined) this.color = data.color;
      if (data.type !== undefined) this.type = data.type;
      if (data.parentId !== undefined) this.parentId = data.parentId;
      if (data.isDefault !== undefined) this.isDefault = data.isDefault;
      if (data.sortOrder !== undefined) this.sortOrder = data.sortOrder;
    }
    if (!this.id) {
      this.id = this.generateId();
    }
  }
  
  private generateId(): string {
    return Date.now().toString() + Math.random().toString(36).substring(2, 11);
  }
}

/**
 * 分类类型枚举
 */
export enum CategoryType {
  INCOME = 'income',   // 收入分类
  EXPENSE = 'expense'  // 支出分类
}

/**
 * 默认分类配置
 */
export class DefaultCategories {
  static getExpenseCategories(): Category[] {
    const categories: Category[] = [];

    const category1 = new Category();
    category1.name = '餐饮';
    category1.icon = '🍽️';
    category1.color = '#FF9800';
    category1.type = CategoryType.EXPENSE;
    category1.isDefault = true;
    category1.sortOrder = 1;
    categories.push(category1);

    const category2 = new Category();
    category2.name = '交通';
    category2.icon = '🚗';
    category2.color = '#2196F3';
    category2.type = CategoryType.EXPENSE;
    category2.isDefault = true;
    category2.sortOrder = 2;
    categories.push(category2);

    const category3 = new Category();
    category3.name = '购物';
    category3.icon = '🛍️';
    category3.color = '#E91E63';
    category3.type = CategoryType.EXPENSE;
    category3.isDefault = true;
    category3.sortOrder = 3;
    categories.push(category3);

    const category4 = new Category();
    category4.name = '其他';
    category4.icon = '📝';
    category4.color = '#9E9E9E';
    category4.type = CategoryType.EXPENSE;
    category4.isDefault = true;
    category4.sortOrder = 99;
    categories.push(category4);

    return categories;
  }

  static getIncomeCategories(): Category[] {
    const categories: Category[] = [];

    const category1 = new Category();
    category1.name = '工资';
    category1.icon = '💰';
    category1.color = '#4CAF50';
    category1.type = CategoryType.INCOME;
    category1.isDefault = true;
    category1.sortOrder = 1;
    categories.push(category1);

    const category2 = new Category();
    category2.name = '其他';
    category2.icon = '📝';
    category2.color = '#9E9E9E';
    category2.type = CategoryType.INCOME;
    category2.isDefault = true;
    category2.sortOrder = 99;
    categories.push(category2);

    return categories;
  }
}
