import { Account, AccountType } from '../models/Account';
import { Category } from '../models/Category';
import { User } from '../models/User';
import { AppConstants } from '../common/constants/AppConstants';
import relationalStore from '@ohos.data.relationalStore';
import { Context } from '@ohos.abilityAccessCtrl';

/**
 * 数据库服务类
 */
export class DatabaseService {
  private static instance: DatabaseService;
  private store: relationalStore.RdbStore | null = null;
  private isInitialized: boolean = false;
  private context: Context | null = null;

  private constructor() {}

  static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  /**
   * 设置上下文
   */
  setContext(context: Context): void {
    this.context = context;
  }

  /**
   * 初始化数据库
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log('初始化数据库');

      if (!this.context) {
        console.log('使用模拟数据库模式');
        this.isInitialized = true;
        return;
      }

      const config: relationalStore.StoreConfig = {
        name: AppConstants.DB_NAME,
        securityLevel: relationalStore.SecurityLevel.S1
      };

      try {
        this.store = await relationalStore.getRdbStore(this.context, config);
        console.log('真实数据库初始化完成');
      } catch (dbError) {
        console.log('数据库初始化失败，使用模拟模式:', dbError);
      }

      // 创建表
      await this.createTables();

      this.isInitialized = true;

    } catch (error) {
      console.error('数据库初始化失败:', error);
      throw new Error('数据库初始化失败');
    }
  }

  /**
   * 保存账单
   */
  async saveAccount(account: Account): Promise<boolean> {
    try {
      console.log('保存账单:', account.id);

      if (this.store) {
        const valueBucket: relationalStore.ValuesBucket = {
          id: account.id,
          amount: account.amount,
          type: account.type,
          category: account.category,
          description: account.description || '',
          date: account.date.getTime(),
          location: account.location || '',
          imageUrl: account.imageUrl || '',
          tags: JSON.stringify(account.tags),
          isAIGenerated: account.isAIGenerated ? 1 : 0,
          aiConfidence: account.aiConfidence || 0,
          updatedAt: Date.now()
        };

        // 检查是否已存在
        const predicates = new relationalStore.RdbPredicates(AppConstants.TABLE_ACCOUNTS);
        predicates.equalTo('id', account.id);

        const resultSet = await this.store.query(predicates);
        const exists = resultSet.rowCount > 0;
        resultSet.close();

        if (exists) {
          // 更新
          await this.store.update(valueBucket, predicates);
        } else {
          // 插入
          await this.store.insert(AppConstants.TABLE_ACCOUNTS, valueBucket);
        }

        console.log('账单保存成功（真实数据库）');
      } else {
        // 模拟保存
        await this.delay(100);
        console.log('账单保存成功（模拟）');
      }

      return true;

    } catch (error) {
      console.error('保存账单失败:', error);
      return false;
    }
  }

  /**
   * 获取账单列表
   */
  async getAccounts(limit?: number, offset?: number): Promise<Account[]> {
    try {
      console.log('获取账单列表');

      if (this.store) {
        const predicates = new relationalStore.RdbPredicates(AppConstants.TABLE_ACCOUNTS);
        predicates.orderByDesc('date');

        if (limit) {
          predicates.limitAs(limit);
        }
        if (offset) {
          predicates.offsetAs(offset);
        }

        const resultSet = await this.store.query(predicates);
        const accounts: Account[] = [];

        if (resultSet.goToFirstRow()) {
          do {
            const account = new Account();
            account.id = resultSet.getString(resultSet.getColumnIndex('id'));
            account.amount = resultSet.getDouble(resultSet.getColumnIndex('amount'));
            account.type = resultSet.getString(resultSet.getColumnIndex('type')) as AccountType;
            account.category = resultSet.getString(resultSet.getColumnIndex('category'));
            account.description = resultSet.getString(resultSet.getColumnIndex('description'));
            account.date = new Date(resultSet.getLong(resultSet.getColumnIndex('date')));
            account.location = resultSet.getString(resultSet.getColumnIndex('location'));
            account.imageUrl = resultSet.getString(resultSet.getColumnIndex('imageUrl'));

            const tagsStr = resultSet.getString(resultSet.getColumnIndex('tags'));
            if (tagsStr) {
              try {
                account.tags = JSON.parse(tagsStr);
              } catch (e) {
                account.tags = [];
              }
            }

            account.isAIGenerated = resultSet.getLong(resultSet.getColumnIndex('isAIGenerated')) === 1;
            account.aiConfidence = resultSet.getDouble(resultSet.getColumnIndex('aiConfidence'));

            accounts.push(account);
          } while (resultSet.goToNextRow());
        }

        resultSet.close();
        console.log('获取到账单（真实数据库）:', accounts.length);
        return accounts;

      } else {
        // 返回模拟数据
        return this.getMockAccounts();
      }

    } catch (error) {
      console.error('获取账单列表失败:', error);
      return this.getMockAccounts();
    }
  }

  /**
   * 根据ID获取账单
   */
  async getAccountById(id: string): Promise<Account | null> {
    try {
      console.log('获取账单:', id);
      
      await this.delay(100);
      
      // 模拟查询结果
      const account = new Account();
      account.id = id;
      account.amount = 25.50;
      account.type = AccountType.EXPENSE;
      account.category = '餐饮';
      account.description = '午餐';
      return account;
      
    } catch (error) {
      console.error('获取账单失败:', error);
      return null;
    }
  }

  /**
   * 更新账单
   */
  async updateAccount(account: Account): Promise<boolean> {
    try {
      console.log('更新账单:', account.id);
      
      await this.delay(100);
      
      console.log('账单更新成功');
      return true;
      
    } catch (error) {
      console.error('更新账单失败:', error);
      return false;
    }
  }

  /**
   * 删除账单
   */
  async deleteAccount(id: string): Promise<boolean> {
    try {
      console.log('删除账单:', id);
      
      await this.delay(100);
      
      console.log('账单删除成功');
      return true;
      
    } catch (error) {
      console.error('删除账单失败:', error);
      return false;
    }
  }

  /**
   * 获取分类列表
   */
  async getCategories(): Promise<Category[]> {
    try {
      console.log('获取分类列表');
      
      await this.delay(100);
      
      // 返回默认分类
      const categories: Category[] = [];

      const cat1 = new Category();
      cat1.name = '餐饮';
      cat1.icon = '🍽️';
      cat1.color = '#FF9800';
      categories.push(cat1);

      const cat2 = new Category();
      cat2.name = '交通';
      cat2.icon = '🚗';
      cat2.color = '#2196F3';
      categories.push(cat2);
      
      console.log('获取到分类:', categories.length);
      return categories;
      
    } catch (error) {
      console.error('获取分类列表失败:', error);
      return [];
    }
  }

  /**
   * 保存分类
   */
  async saveCategory(category: Category): Promise<boolean> {
    try {
      console.log('保存分类:', category.name);
      
      await this.delay(100);
      
      console.log('分类保存成功');
      return true;
      
    } catch (error) {
      console.error('保存分类失败:', error);
      return false;
    }
  }

  /**
   * 获取用户信息
   */
  async getUser(): Promise<User | null> {
    try {
      console.log('获取用户信息');
      
      await this.delay(100);
      
      const user = new User();
      user.username = 'MonterAI用户';
      user.email = '<EMAIL>';
      return user;
      
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return null;
    }
  }

  /**
   * 保存用户信息
   */
  async saveUser(user: User): Promise<boolean> {
    try {
      console.log('保存用户信息:', user.username);
      
      await this.delay(100);
      
      console.log('用户信息保存成功');
      return true;
      
    } catch (error) {
      console.error('保存用户信息失败:', error);
      return false;
    }
  }

  /**
   * 按日期范围查询账单
   */
  async getAccountsByDateRange(startDate: Date, endDate: Date): Promise<Account[]> {
    try {
      console.log('按日期范围查询账单:', startDate, endDate);
      
      await this.delay(200);
      
      // 模拟查询结果
      const accounts = await this.getAccounts();
      return accounts.filter(account => 
        account.date >= startDate && account.date <= endDate
      );
      
    } catch (error) {
      console.error('按日期范围查询账单失败:', error);
      return [];
    }
  }

  /**
   * 按分类查询账单
   */
  async getAccountsByCategory(category: string): Promise<Account[]> {
    try {
      console.log('按分类查询账单:', category);
      
      await this.delay(200);
      
      const accounts = await this.getAccounts();
      return accounts.filter(account => account.category === category);
      
    } catch (error) {
      console.error('按分类查询账单失败:', error);
      return [];
    }
  }

  /**
   * 获取统计数据
   */
  async getStatistics(startDate: Date, endDate: Date): Promise<StatisticsData> {
    try {
      console.log('获取统计数据');
      
      await this.delay(300);
      
      const accounts = await this.getAccountsByDateRange(startDate, endDate);
      
      const totalIncome = accounts
        .filter(a => a.type === AccountType.INCOME)
        .reduce((sum, a) => sum + a.amount, 0);
        
      const totalExpense = accounts
        .filter(a => a.type === AccountType.EXPENSE)
        .reduce((sum, a) => sum + a.amount, 0);
      
      return {
        totalIncome,
        totalExpense,
        balance: totalIncome - totalExpense,
        accountCount: accounts.length,
        categoryStats: this.calculateCategoryStats(accounts)
      };
      
    } catch (error) {
      console.error('获取统计数据失败:', error);
      return {
        totalIncome: 0,
        totalExpense: 0,
        balance: 0,
        accountCount: 0,
        categoryStats: []
      };
    }
  }

  // 私有方法
  private async createTables(): Promise<void> {
    try {
      console.log('创建数据表');

      if (this.store) {
        // 创建账单表
        const createAccountsTable = `
          CREATE TABLE IF NOT EXISTS ${AppConstants.TABLE_ACCOUNTS} (
            id TEXT PRIMARY KEY,
            amount REAL NOT NULL,
            type TEXT NOT NULL,
            category TEXT NOT NULL,
            description TEXT,
            date INTEGER NOT NULL,
            location TEXT,
            imageUrl TEXT,
            tags TEXT,
            isAIGenerated INTEGER DEFAULT 0,
            aiConfidence REAL,
            createdAt INTEGER DEFAULT (strftime('%s', 'now')),
            updatedAt INTEGER DEFAULT (strftime('%s', 'now'))
          )
        `;

        // 创建分类表
        const createCategoriesTable = `
          CREATE TABLE IF NOT EXISTS ${AppConstants.TABLE_CATEGORIES} (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            icon TEXT,
            color TEXT,
            type TEXT NOT NULL,
            parentId TEXT,
            isDefault INTEGER DEFAULT 0,
            sortOrder INTEGER DEFAULT 0,
            createdAt INTEGER DEFAULT (strftime('%s', 'now'))
          )
        `;

        // 创建用户表
        const createUsersTable = `
          CREATE TABLE IF NOT EXISTS ${AppConstants.TABLE_USERS} (
            id TEXT PRIMARY KEY,
            username TEXT,
            avatar TEXT,
            email TEXT,
            phone TEXT,
            settings TEXT,
            createdAt INTEGER DEFAULT (strftime('%s', 'now')),
            lastLoginAt INTEGER
          )
        `;

        await this.store.executeSql(createAccountsTable);
        await this.store.executeSql(createCategoriesTable);
        await this.store.executeSql(createUsersTable);

        console.log('真实数据表创建完成');
      } else {
        console.log('模拟数据表创建完成');
      }

    } catch (error) {
      console.error('创建数据表失败:', error);
      throw new Error('创建数据表失败');
    }
  }

  private calculateCategoryStats(accounts: Account[]): CategoryStats[] {
    const categoryMap = new Map<string, number>();
    
    accounts
      .filter(a => a.type === AccountType.EXPENSE)
      .forEach(account => {
        const current = categoryMap.get(account.category) || 0;
        categoryMap.set(account.category, current + account.amount);
      });
    
    const result: CategoryStats[] = [];
    categoryMap.forEach((amount: number, category: string) => {
      const stats: CategoryStats = {
        category: category,
        amount: amount,
        count: accounts.filter(a => a.category === category).length
      };
      result.push(stats);
    });
    return result;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private getMockAccounts(): Account[] {
    const accounts: Account[] = [];

    const account1 = new Account();
    account1.amount = 25.50;
    account1.type = AccountType.EXPENSE;
    account1.category = '餐饮';
    account1.description = '午餐';
    account1.date = new Date();
    account1.location = '公司附近';
    accounts.push(account1);

    const account2 = new Account();
    account2.amount = 8000.00;
    account2.type = AccountType.INCOME;
    account2.category = '工资';
    account2.description = '月薪';
    account2.date = new Date(Date.now() - 24 * 60 * 60 * 1000);
    accounts.push(account2);

    const account3 = new Account();
    account3.amount = 120.00;
    account3.type = AccountType.EXPENSE;
    account3.category = '交通';
    account3.description = '地铁卡充值';
    account3.date = new Date(Date.now() - 2 * 24 * 60 * 60 * 1000);
    accounts.push(account3);

    return accounts;
  }
}

// 类型定义
export interface StatisticsData {
  totalIncome: number;
  totalExpense: number;
  balance: number;
  accountCount: number;
  categoryStats: CategoryStats[];
}

export interface CategoryStats {
  category: string;
  amount: number;
  count: number;
}
