import { Account, AccountType } from '../models/Account';
import { FormatUtils } from '../common/utils/FormatUtils';
import { DateUtils } from '../common/utils/DateUtils';

/**
 * 账单卡片组件
 */
@Component
export struct AccountCard {
  @Prop account: Account = new Account();
  @Prop showDate: boolean = true;
  onItemClick?: (account: Account) => void;
  onItemLongPress?: (account: Account) => void;

  build() {
    Row() {
      // 分类图标
      Row() {
        Text(this.getCategoryIcon())
          .fontSize(24)
          .fontColor('#FFFFFF')
      }
      .width(48)
      .height(48)
      .borderRadius(24)
      .backgroundColor(this.getCategoryColor())
      .justifyContent(FlexAlign.Center)
      .alignItems(VerticalAlign.Center)

      // 账单信息
      Column() {
        Row() {
          Text(this.account.category || '未分类')
            .fontSize(16)
            .fontWeight(FontWeight.Medium)
            .fontColor('#212121')
            .maxLines(1)
            .textOverflow({ overflow: TextOverflow.Ellipsis })
            .layoutWeight(1)

          Text(this.account.getFormattedAmount())
            .fontSize(16)
            .fontWeight(FontWeight.Bold)
            .fontColor(this.account.getAmountColor())
        }
        .width('100%')
        .justifyContent(FlexAlign.SpaceBetween)

        if (this.account.description) {
          Text(this.account.description)
            .fontSize(14)
            .fontColor('#757575')
            .maxLines(1)
            .textOverflow({ overflow: TextOverflow.Ellipsis })
            .margin({ top: 4 })
        }

        if (this.showDate) {
          Row() {
            Text(DateUtils.formatDate(this.account.date, 'MM-DD HH:mm'))
              .fontSize(12)
              .fontColor('#9E9E9E')

            if (this.account.isAIGenerated) {
              Text('AI')
                .fontSize(10)
                .fontColor('#FFFFFF')
                .backgroundColor('#2196F3')
                .padding({ left: 4, right: 4, top: 1, bottom: 1 })
                .borderRadius(2)
                .margin({ left: 8 })
            }

            if (this.account.location) {
              Text(this.account.location)
                .fontSize(12)
                .fontColor('#9E9E9E')
                .maxLines(1)
                .textOverflow({ overflow: TextOverflow.Ellipsis })
                .margin({ left: 8 })
                .layoutWeight(1)
            }
          }
          .width('100%')
          .margin({ top: 4 })
        }
      }
      .layoutWeight(1)
      .margin({ left: 12 })
      .alignItems(HorizontalAlign.Start)
    }
    .width('100%')
    .padding(16)
    .backgroundColor('#FFFFFF')
    .borderRadius(8)
    .shadow({
      radius: 4,
      color: '#1F000000',
      offsetX: 0,
      offsetY: 2
    })
    .onClick(() => {
      if (this.onItemClick) {
        this.onItemClick(this.account);
      }
    })
    .gesture(
      LongPressGesture({ repeat: false })
        .onAction(() => {
          if (this.onItemLongPress) {
            this.onItemLongPress(this.account);
          }
        })
    )
  }

  private getCategoryIcon(): string {
    // 根据分类返回对应图标，这里简化处理
    const iconMap: Record<string, string> = {
      '餐饮': '🍽️',
      '交通': '🚗',
      '购物': '🛍️',
      '娱乐': '🎮',
      '医疗': '🏥',
      '教育': '📚',
      '住房': '🏠',
      '工资': '💰',
      '奖金': '🎁',
      '投资': '📈',
      '兼职': '💼'
    };
    return iconMap[this.account.category] || '📝';
  }

  private getCategoryColor(): string {
    // 根据分类返回对应颜色，这里简化处理
    const colorMap: Record<string, string> = {
      '餐饮': '#FF9800',
      '交通': '#2196F3',
      '购物': '#E91E63',
      '娱乐': '#9C27B0',
      '医疗': '#F44336',
      '教育': '#607D8B',
      '住房': '#795548',
      '工资': '#4CAF50',
      '奖金': '#8BC34A',
      '投资': '#CDDC39',
      '兼职': '#FFC107'
    };
    return colorMap[this.account.category] || '#9E9E9E';
  }
}
