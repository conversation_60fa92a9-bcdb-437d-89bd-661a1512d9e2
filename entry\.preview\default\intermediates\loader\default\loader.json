{"modulePathMap": {"entry": "E:\\Development\\Project\\Monter\\entry"}, "compileMode": "esmodule", "projectRootPath": "E:\\Development\\Project\\Monter", "nodeModulesPath": "E:\\Development\\Project\\Monter\\entry\\.preview\\default\\intermediates\\loader_out\\default\\node_modules", "byteCodeHarInfo": {}, "declarationEntry": [], "moduleName": "entry", "hspNameOhmMap": {}, "harNameOhmMap": {}, "packageManagerType": "ohpm", "compileEntry": [], "otherCompileFiles": [], "dynamicImportLibInfo": {}, "routerMap": [], "hspResourcesMap": {}, "updateVersionInfo": {}, "anBuildOutPut": "E:\\Development\\Project\\Monter\\entry\\.preview\\default\\intermediates\\loader_out\\default\\an\\arm64-v8a", "anBuildMode": "type", "buildConfigPath": ".preview\\config\\buildConfig.json"}