if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface SettingsPage_Params {
    user?: User;
    settings?: UserSettings;
    appVersion?: string;
    syncService?: SyncService;
}
import { User, UserSettings, ThemeMode } from "@normalized:N&&&entry/src/main/ets/models/User&";
import { SyncService } from "@normalized:N&&&entry/src/main/ets/services/SyncService&";
import router from "@ohos:router";
class SettingsPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__user = new ObservedPropertyObjectPU(new User(), this, "user");
        this.__settings = new ObservedPropertyObjectPU(new UserSettings(), this, "settings");
        this.__appVersion = new ObservedPropertySimplePU('1.0.0', this, "appVersion");
        this.syncService = SyncService.getInstance();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: SettingsPage_Params) {
        if (params.user !== undefined) {
            this.user = params.user;
        }
        if (params.settings !== undefined) {
            this.settings = params.settings;
        }
        if (params.appVersion !== undefined) {
            this.appVersion = params.appVersion;
        }
        if (params.syncService !== undefined) {
            this.syncService = params.syncService;
        }
    }
    updateStateVars(params: SettingsPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__user.purgeDependencyOnElmtId(rmElmtId);
        this.__settings.purgeDependencyOnElmtId(rmElmtId);
        this.__appVersion.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__user.aboutToBeDeleted();
        this.__settings.aboutToBeDeleted();
        this.__appVersion.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __user: ObservedPropertyObjectPU<User>;
    get user() {
        return this.__user.get();
    }
    set user(newValue: User) {
        this.__user.set(newValue);
    }
    private __settings: ObservedPropertyObjectPU<UserSettings>;
    get settings() {
        return this.__settings.get();
    }
    set settings(newValue: UserSettings) {
        this.__settings.set(newValue);
    }
    private __appVersion: ObservedPropertySimplePU<string>;
    get appVersion() {
        return this.__appVersion.get();
    }
    set appVersion(newValue: string) {
        this.__appVersion.set(newValue);
    }
    private syncService: SyncService;
    aboutToAppear() {
        this.loadUserSettings();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(19:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        // 顶部导航栏
        this.buildHeader.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 内容区域
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/SettingsPage.ets(24:7)", "entry");
            // 内容区域
            Scroll.layoutWeight(1);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(25:9)", "entry");
            Column.padding(16);
        }, Column);
        // 用户信息
        this.buildUserProfile.bind(this)();
        // 记账设置
        this.buildAccountSettings.bind(this)();
        // AI设置
        this.buildAISettings.bind(this)();
        // 同步设置
        this.buildSyncSettings.bind(this)();
        // 通用设置
        this.buildGeneralSettings.bind(this)();
        // 关于应用
        this.buildAboutSection.bind(this)();
        Column.pop();
        // 内容区域
        Scroll.pop();
        Column.pop();
    }
    buildHeader(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(55:5)", "entry");
            Row.width('100%');
            Row.padding({ left: 16, right: 16, top: 12, bottom: 12 });
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 返回按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(57:7)", "entry");
            // 返回按钮
            Row.width(40);
            // 返回按钮
            Row.height(40);
            // 返回按钮
            Row.borderRadius(20);
            // 返回按钮
            Row.justifyContent(FlexAlign.Center);
            // 返回按钮
            Row.alignItems(VerticalAlign.Center);
            // 返回按钮
            Row.onClick(() => {
                router.back();
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('←');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(58:9)", "entry");
            Text.fontSize(20);
            Text.fontColor('#212121');
        }, Text);
        Text.pop();
        // 返回按钮
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('设置');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(71:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 占位
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(79:7)", "entry");
            // 占位
            Row.width(40);
            // 占位
            Row.height(40);
        }, Row);
        // 占位
        Row.pop();
        Row.pop();
    }
    buildUserProfile(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(90:5)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(91:7)", "entry");
            Row.width('100%');
            Row.padding(20);
            Row.backgroundColor('#FFFFFF');
            Row.borderRadius(12);
            Row.onClick(() => {
                console.log('编辑用户信息');
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 头像
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(93:9)", "entry");
            // 头像
            Row.width(60);
            // 头像
            Row.height(60);
            // 头像
            Row.borderRadius(30);
            // 头像
            Row.backgroundColor('#1976D2');
            // 头像
            Row.justifyContent(FlexAlign.Center);
            // 头像
            Row.alignItems(VerticalAlign.Center);
            // 头像
            Row.margin({ right: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('👤');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(94:11)", "entry");
            Text.fontSize(32);
            Text.fontColor('#FFFFFF');
        }, Text);
        Text.pop();
        // 头像
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 用户信息
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(107:9)", "entry");
            // 用户信息
            Column.layoutWeight(1);
            // 用户信息
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.user.username || '用户');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(108:11)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.user.email || '未设置邮箱');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(114:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#757575');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 用户信息
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('>');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(123:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#CCCCCC');
        }, Text);
        Text.pop();
        Row.pop();
        Column.pop();
    }
    buildAccountSettings(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(141:5)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('记账设置');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(142:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        // 默认货币
        this.buildSettingItem.bind(this)('💰', '默认货币', this.settings.currency, () => {
            console.log('设置默认货币');
        });
        // 月预算
        this.buildSettingItem.bind(this)('📊', '月预算', `¥${this.settings.monthlyBudget}`, () => {
            console.log('设置月预算');
        });
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 预算提醒
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(160:7)", "entry");
            // 预算提醒
            Row.width('100%');
            // 预算提醒
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(161:9)", "entry");
            Row.width('100%');
            Row.padding(16);
            Row.backgroundColor('#FFFFFF');
            Row.borderRadius(8);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🔔');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(162:11)", "entry");
            Text.fontSize(20);
            Text.margin({ right: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(166:11)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('预算提醒');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(167:13)", "entry");
            Text.fontSize(14);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('超出预算时提醒');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(172:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#757575');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ top: 2 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Toggle.create({ type: ToggleType.Switch, isOn: this.settings.enableBudgetNotification });
            Toggle.debugLine("entry/src/main/ets/pages/SettingsPage.ets(181:11)", "entry");
            Toggle.onChange((isOn: boolean) => {
                this.settings.enableBudgetNotification = isOn;
                this.saveSettings();
            });
        }, Toggle);
        Toggle.pop();
        Row.pop();
        // 预算提醒
        Row.pop();
        Column.pop();
    }
    buildAISettings(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(201:5)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('AI设置');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(202:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 语音识别
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(210:7)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(211:9)", "entry");
            Row.width('100%');
            Row.padding(16);
            Row.backgroundColor('#FFFFFF');
            Row.borderRadius(8);
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🎤');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(212:11)", "entry");
            Text.fontSize(20);
            Text.margin({ right: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(216:11)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('语音识别');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(217:13)", "entry");
            Text.fontSize(14);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('启用语音记账功能');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(222:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#757575');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ top: 2 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Toggle.create({ type: ToggleType.Switch, isOn: this.settings.enableVoiceInput });
            Toggle.debugLine("entry/src/main/ets/pages/SettingsPage.ets(231:11)", "entry");
            Toggle.onChange((isOn: boolean) => {
                this.settings.enableVoiceInput = isOn;
                this.saveSettings();
            });
        }, Toggle);
        Toggle.pop();
        Row.pop();
        // 语音识别
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 拍照识别
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(245:7)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(246:9)", "entry");
            Row.width('100%');
            Row.padding(16);
            Row.backgroundColor('#FFFFFF');
            Row.borderRadius(8);
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📷');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(247:11)", "entry");
            Text.fontSize(20);
            Text.margin({ right: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(251:11)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('拍照识别');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(252:13)", "entry");
            Text.fontSize(14);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('启用小票拍照识别');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(257:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#757575');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ top: 2 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Toggle.create({ type: ToggleType.Switch, isOn: this.settings.enablePhotoRecognition });
            Toggle.debugLine("entry/src/main/ets/pages/SettingsPage.ets(266:11)", "entry");
            Toggle.onChange((isOn: boolean) => {
                this.settings.enablePhotoRecognition = isOn;
                this.saveSettings();
            });
        }, Toggle);
        Toggle.pop();
        Row.pop();
        // 拍照识别
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // AI分析
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(280:7)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(281:9)", "entry");
            Row.width('100%');
            Row.padding(16);
            Row.backgroundColor('#FFFFFF');
            Row.borderRadius(8);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🤖');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(282:11)", "entry");
            Text.fontSize(20);
            Text.margin({ right: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(286:11)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('AI分析');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(287:13)", "entry");
            Text.fontSize(14);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('启用智能消费分析');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(292:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#757575');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ top: 2 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Toggle.create({ type: ToggleType.Switch, isOn: this.settings.enableAIAnalysis });
            Toggle.debugLine("entry/src/main/ets/pages/SettingsPage.ets(301:11)", "entry");
            Toggle.onChange((isOn: boolean) => {
                this.settings.enableAIAnalysis = isOn;
                this.saveSettings();
            });
        }, Toggle);
        Toggle.pop();
        Row.pop();
        // AI分析
        Row.pop();
        Column.pop();
    }
    buildSyncSettings(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(319:5)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('同步设置');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(320:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        // 设备管理
        this.buildSettingItem.bind(this)('📱', '设备管理', '管理同步设备', () => {
            router.pushUrl({
                url: 'pages/DeviceManagePage'
            });
        });
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 分布式同步
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(335:7)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(336:9)", "entry");
            Row.width('100%');
            Row.padding(16);
            Row.backgroundColor('#FFFFFF');
            Row.borderRadius(8);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🔄');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(337:11)", "entry");
            Text.fontSize(20);
            Text.margin({ right: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(341:11)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('分布式同步');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(342:13)", "entry");
            Text.fontSize(14);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('多设备数据同步');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(347:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#757575');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ top: 2 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Toggle.create({ type: ToggleType.Switch, isOn: this.settings.enableDistributedSync });
            Toggle.debugLine("entry/src/main/ets/pages/SettingsPage.ets(356:11)", "entry");
            Toggle.onChange((isOn: boolean) => {
                this.settings.enableDistributedSync = isOn;
                this.saveSettings();
            });
        }, Toggle);
        Toggle.pop();
        Row.pop();
        // 分布式同步
        Row.pop();
        Column.pop();
    }
    buildGeneralSettings(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(374:5)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('通用设置');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(375:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        // 主题设置
        this.buildSettingItem.bind(this)('🎨', '主题', this.getThemeText(), () => {
            console.log('设置主题');
        });
        // 语言设置
        this.buildSettingItem.bind(this)('🌐', '语言', '简体中文', () => {
            console.log('设置语言');
        });
        // 隐私设置
        this.buildSettingItem.bind(this)('🔒', '隐私设置', '数据加密和权限', () => {
            console.log('隐私设置');
        });
        Column.pop();
    }
    buildAboutSection(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(403:5)", "entry");
            Column.width('100%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('关于');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(404:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        // 版本信息
        this.buildSettingItem.bind(this)('ℹ️', '版本', `v${this.appVersion}`, () => {
            console.log('版本信息');
        });
        // 帮助与反馈
        this.buildSettingItem.bind(this)('❓', '帮助与反馈', '使用指南和问题反馈', () => {
            console.log('帮助与反馈');
        });
        // 隐私政策
        this.buildSettingItem.bind(this)('📄', '隐私政策', '查看隐私政策', () => {
            console.log('隐私政策');
        });
        Column.pop();
    }
    buildSettingItem(icon: string, title: string, subtitle: string, onClick: () => void, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(431:5)", "entry");
            Row.width('100%');
            Row.padding(16);
            Row.backgroundColor('#FFFFFF');
            Row.borderRadius(8);
            Row.margin({ bottom: 8 });
            Row.onClick(onClick);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(icon);
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(432:7)", "entry");
            Text.fontSize(20);
            Text.margin({ right: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(436:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(title);
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(437:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#212121');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (subtitle) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(subtitle);
                        Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(443:11)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#757575');
                        Text.alignSelf(ItemAlign.Start);
                        Text.margin({ top: 2 });
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('>');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(453:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#CCCCCC');
        }, Text);
        Text.pop();
        Row.pop();
    }
    // 私有方法
    private loadUserSettings() {
        // 模拟加载用户设置
        this.user.username = 'MonterAI用户';
        this.user.email = '<EMAIL>';
        this.settings.monthlyBudget = 3000;
    }
    private saveSettings() {
        console.log('保存设置:', this.settings);
        // TODO: 保存设置到本地存储
    }
    private getThemeText(): string {
        switch (this.settings.theme) {
            case ThemeMode.LIGHT:
                return '浅色模式';
            case ThemeMode.DARK:
                return '深色模式';
            case ThemeMode.AUTO:
                return '跟随系统';
            default:
                return '跟随系统';
        }
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "SettingsPage";
    }
}
registerNamedRoute(() => new SettingsPage(undefined, {}), "", { bundleName: "com.silence.monter", moduleName: "entry", pagePath: "pages/SettingsPage", pageFullPath: "entry/src/main/ets/pages/SettingsPage", integratedHsp: "false", moduleType: "followWithHap" });
